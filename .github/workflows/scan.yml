name: "<PERSON> Scan (AI-Agent Team)"

on:
  push:
    branches:
      - main
  # Removed PR scanning for AI-agent teams - only scan main branch
  schedule:
    - cron: "0 14 * * 1"  # Weekly on Monday instead of Friday

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        language:
          - python
          - javascript

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - run: git checkout HEAD^2
        if: ${{ github.event_name == 'pull_request' }}
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          config-file: ./.github/codeql/codeql-config.yml
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Generate template project
        run: invoke build
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
