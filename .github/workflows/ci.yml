name: AI-Agent Team CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

permissions:
  contents: read

jobs:
  ai-agent-checks:
    name: AI-Agent Systematic Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'  # Single version for AI-agent teams

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r promptyoself/requirements-dev.txt

      - name: Install Playwright browsers
        run: playwright install --with-deps

      - name: Run core tests (systematic error prevention)
        run: |
          PYTHONPATH=promptyoself pytest promptyoself/tests/ \
            --tb=short \
            -x \
            --disable-warnings

      - name: Run Playwright E2E tests
        run: |
          PYTHONPATH=promptyoself pytest promptyoself/tests_e2e/ -m playwright \
            --tb=short --disable-warnings

      - name: Essential linting (syntax & import errors only)
        run: |
          python -m flake8 promptyoself/ --select=E9,F63,F7,F82 --show-source --statistics

      - name: Verify Docker build
        run: |
          docker build -t promptyoself:ci-check .