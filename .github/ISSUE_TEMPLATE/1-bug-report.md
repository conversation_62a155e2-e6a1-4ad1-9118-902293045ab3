---
name: "\U0001F41B Bug report"
about: Report a bug to help us fix it
title: ""
labels: bug
assignees: ""
---

<!--
Thank you for helping make this project better!

Please provide minimum reproducible examples and screenshots where relevant.
Always feel free to submit PRs for any bugs you discover.

Additionally, please complete the following section with the options you used to generate the project where you observe the bug.
-->

Project configuration:

| Option                               | Values                                                                                      |
| ------------------------------------ | ------------------------------------------------------------------------------------------- |
| `use_pipenv`                         | <ul><li>- [ ] `yes`</li><li>- [ ] `no`</li></ul>                                            |
| `python_version`                     | <ul><li>- [ ] `3.13`</li><li>- [ ] `3.12`</li></ul> |
| `node_version`                       | <ul><li>- [ ] `22`</li><li>- [ ] `20`</li></ul>                                             |
| `use_heroku`                         | <ul><li>- [ ] `yes`</li><li>- [ ] `no`</li></ul>                                            |
| Are you using Docker to run the app? | <ul><li>- [ ] `yes`</li><li>- [ ] `no`</li></ul>                                            |
