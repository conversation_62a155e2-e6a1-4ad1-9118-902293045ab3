version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/{{cookiecutter.app_name}}"
    schedule:
      interval: "weekly"

  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"

  - package-ecosystem: "github-actions"
    directory: "/{{cookiecutter.app_name}}/.github"
    schedule:
      interval: "weekly"

  - package-ecosystem: "pip"
    directory: "/{{cookiecutter.app_name}}/requirements"
    schedule:
      interval: "weekly"

  - package-ecosystem: "pip"
    directory: "/{{cookiecutter.app_name}}"
    schedule:
      interval: "weekly"

  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
