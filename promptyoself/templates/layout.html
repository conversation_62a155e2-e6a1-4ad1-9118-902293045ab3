<!DOCTYPE html>
<!-- paulirish.com/2008/conditional-stylesheets-vs-css-hacks-answer-neither/ -->
<!--[if lt IE 7]> <html class="no-js lt-ie9 lt-ie8 lt-ie7" lang="en"> <![endif]-->
<!--[if IE 7]>    <html class="no-js lt-ie9 lt-ie8" lang="en"> <![endif]-->
<!--[if IE 8]>    <html class="no-js lt-ie9" lang="en"> <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8" />

  <link rel="shortcut icon" href="{{ url_for('static', filename='build/favicon.ico') }}">
  <title>
    {% block page_title %}
    pyv3
    {% endblock %}
  </title>
  <meta name="description" content="{% block meta_description %}{% endblock %}" />
  <meta name="author" content="{% block meta_author %}{% endblock %}" />

  <!-- Mobile viewport optimized: h5bp.com/viewport -->
  <meta name="viewport" content="width=device-width" />

  <script src="https://cdn.tailwindcss.com"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='build/main_css.bundle.css') }}" />

  {% block css %}{% endblock %}
</head>

<body class="bg-gray-100 text-gray-900 antialiased {% block body_class %}{% endblock %}">
  <a href="#main-content"
    class="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 focus:bg-white focus:text-blue-600 p-2">Skip
    to main content</a>
  {% block body %} {% with form=form %} {% include "nav.html" %} {% endwith %}

  <header role="banner" class="container mx-auto px-4 py-6">{% block header %}{% endblock %}</header>

  <main id="main-content" role="main" class="container mx-auto px-4 py-8">
    {% with messages = get_flashed_messages(with_categories=true) %} {% if
    messages %}
    <div class="mb-4">
      {% for category, message in messages %}
      {% set alert_class = 'bg-blue-100 border-blue-500 text-blue-700' %}
      {% if category == 'error' or category == 'danger' %}
      {% set alert_class = 'bg-red-100 border-red-500 text-red-700' %}
      {% elif category == 'success' %}
      {% set alert_class = 'bg-green-100 border-green-500 text-green-700' %}
      {% elif category == 'warning' %}
      {% set alert_class = 'bg-yellow-100 border-yellow-500 text-yellow-700' %}
      {% endif %}
      <div class="border-l-4 p-4 {{ alert_class }}" role="alert">
        <div class="flex">
          <div class="py-1">
            <!-- Optional: Icon can be added here -->
          </div>
          <div>
            <p class="font-bold">{{ category|capitalize }}</p>
            <p class="text-sm">{{ message }}</p>
          </div>
          <button type="button"
            class="ml-auto -mx-1.5 -my-1.5 bg-transparent text-gray-500 rounded-lg focus:ring-2 focus:ring-gray-400 p-1.5 hover:bg-gray-200 inline-flex h-8 w-8"
            onclick="this.closest('.border-l-4').remove()" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
      {% endfor %}
    </div>
    {% endif %} {% endwith %} {% block content %}{% endblock %}
  </main>

  {% include "footer.html" %}

  <!-- JavaScript at the bottom for fast page loading -->
  <script src="{{ url_for('static', filename='build/main_js.bundle.js') }}"></script>
  {% block js %}{% endblock %}
  <!-- end scripts -->
  {% endblock %}
</body>

</html>