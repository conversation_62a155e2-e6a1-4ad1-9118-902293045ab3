{% extends "layout.html" %}

{% block page_title %}Project Details: {{ project.name }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <a href="{{ url_for('projects.list_projects') }}" class="text-indigo-600 hover:text-indigo-800 text-sm">&larr; Back to Projects List</a>
    </div>
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Project: {{ project.name }}</h1>

    <div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-700">Project Information</h2>
        </div>
        <div class="px-6 py-4">
            <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">ID</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ project.id }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ project.name }}</dd>
                </div>
                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                    <dd class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ project.description }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Created At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ project.created_at.strftime('%Y-%m-%d %H:%M') if project.created_at else 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Updated At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ project.updated_at.strftime('%Y-%m-%d %H:%M') if project.updated_at else 'N/A' }}</dd>
                </div>
            </dl>
        </div>
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 justify-end">
            <a href="{{ url_for('projects.edit_project', project_id=project.id) }}" class="px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 text-center">Edit Project</a>
            <form action="{{ url_for('projects.delete_project', project_id=project.id) }}" method="POST" class="inline-block">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <button type="submit" class="w-full sm:w-auto px-4 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this project and all its tasks/reminders?');">Delete Project</button>
            </form>
        </div>
    </div>

    <div class="flex justify-between items-center mb-4">
      <h2 class="text-2xl font-semibold text-gray-700">Associated Tasks</h2>
      <a href="{{ url_for('tasks.new_task', project_id=project.id) }}" class="px-4 py-2 bg-green-500 text-white text-sm rounded-md hover:bg-green-600">Add New Task</a>
    </div>
    {% if project.tasks %}
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for task in project.tasks %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{{ url_for('tasks.view_task', task_id=task.id) }}" class="text-indigo-600 hover:text-indigo-900">{{ task.name }}</a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ task.status.value if task.status else 'N/A' }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ task.priority.value if task.priority else 'N/A' }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'N/A' }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="{{ url_for('tasks.view_task', task_id=task.id) }}" class="px-3 py-1 text-xs text-white bg-green-500 rounded-md hover:bg-green-600">View</a>
                            <a href="{{ url_for('tasks.edit_task', task_id=task.id) }}" class="px-3 py-1 text-xs text-white bg-blue-500 rounded-md hover:bg-blue-600">Edit</a>
                            <form action="{{ url_for('tasks.delete_task', task_id=task.id) }}" method="POST" class="inline-block">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <button type="submit" class="px-3 py-1 text-xs text-white bg-red-500 rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this task?');">Delete</button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="bg-white shadow-md rounded-lg p-6 text-center text-gray-500">
            <p>No tasks associated with this project yet.</p>
        </div>
    {% endif %}
</div>
{% endblock %}