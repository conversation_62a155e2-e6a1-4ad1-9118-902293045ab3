{% extends "layout.html" %}

{% block page_title %}Projects{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-4 md:mb-0">Projects</h1>
        <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4 w-full md:w-auto">
            <a href="{{ url_for('projects.new_project') }}" class="w-full md:w-auto px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-center">New Project</a>
            <form method="GET" action="{{ url_for('projects.list_projects') }}" class="flex w-full md:w-auto">
                <input type="text" name="name" class="px-3 py-2 border border-gray-300 rounded-l-md focus:ring-indigo-500 focus:border-indigo-500 flex-grow" placeholder="Filter by name" value="{{ name_filter or '' }}">
                <button type="submit" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-r-md hover:bg-gray-300">Filter</button>
            </form>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200" role="table" aria-label="Project list">
            <thead class="bg-gray-50" role="rowgroup">
                <tr role="row">
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" role="rowgroup">
                {% for project in paginated_projects.items %}
                <tr role="row">
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <a href="{{ url_for('projects.view_project', project_id=project.id) }}" class="text-indigo-600 hover:text-indigo-900">{{ project.name }}</a>
                    </td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ project.description|truncate(80) }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ project.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <a href="{{ url_for('projects.edit_project', project_id=project.id) }}" class="px-3 py-1 text-xs text-white bg-blue-500 rounded-md hover:bg-blue-600">Edit</a>
                        <form action="{{ url_for('projects.delete_project', project_id=project.id) }}" method="POST" class="inline-block">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="px-3 py-1 text-xs text-white bg-red-500 rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this project and all its tasks/reminders?');">Delete</button>
                        </form>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No projects found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if paginated_projects.pages > 1 %}
    <nav class="mt-6" aria-label="Projects pagination">
        <ul class="flex justify-center items-center space-x-1" role="list">
            {% if paginated_projects.has_prev %}
                <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('projects.list_projects', page=paginated_projects.prev_num, name=name_filter) }}">Previous</a></li>
            {% else %}
                <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-400 bg-gray-50 border border-gray-300 rounded-l-lg cursor-not-allowed">Previous</span></li>
            {% endif %}
    
            {% for page_num in paginated_projects.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                {% if page_num %}
                    {% if paginated_projects.page == page_num %}
                        <li role="listitem"><span class="px-3 py-2 leading-tight text-indigo-600 bg-indigo-50 border border-indigo-300 hover:bg-indigo-100 hover:text-indigo-700 z-10" aria-current="page">{{ page_num }}</span></li>
                    {% else %}
                        <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('projects.list_projects', page=page_num, name=name_filter) }}">{{ page_num }}</a></li>
                    {% endif %}
                {% else %}
                    <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300">...</span></li>
                {% endif %}
            {% endfor %}
    
            {% if paginated_projects.has_next %}
                <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('projects.list_projects', page=paginated_projects.next_num, name=name_filter) }}">Next</a></li>
            {% else %}
                <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-400 bg-gray-50 border border-gray-300 rounded-r-lg cursor-not-allowed">Next</span></li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}