{% extends "layout.html" %}
{% from "macros/form_helpers.html" import render_field %}

{% block page_title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-2xl">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">{{ title }}</h1>
    <form method="POST" action="{{ request.path }}" class="bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4" role="form">
        {{ form.hidden_tag() }}
        {{ render_field(form.name, placeholder="Enter project name") }}
        {{ render_field(form.description, placeholder="Enter project description", rows=5) }}
        
        <div class="flex items-center justify-end mt-6 space-x-3">
            <a href="{{ url_for('projects.list_projects') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
            <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">Save Project</button>
        </div>
    </form>
</div>
{% endblock %}