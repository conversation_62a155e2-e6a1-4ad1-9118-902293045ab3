{% extends "layout.html" %}

{% block page_title %}{{ task.name }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex justify-between items-start mb-6">
            <h1 class="text-3xl font-bold text-gray-800">{{ task.name }}</h1>
            <div class="flex space-x-2">
                <a href="{{ url_for('tasks.edit_task', task_id=task.id) }}" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">Edit</a>
                <a href="{{ url_for('tasks.list_tasks', project_id=task.project_id) }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Back to Tasks</a>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Status</h3>
                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                    {% if task.status == 'completed' %}bg-green-100 text-green-800
                    {% elif task.status == 'in_progress' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ task.status.replace('_', ' ').title() }}
                </span>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Priority</h3>
                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                    {% if task.priority == 'high' %}bg-red-100 text-red-800
                    {% elif task.priority == 'medium' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-green-100 text-green-800{% endif %}">
                    {{ task.priority.title() }}
                </span>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Due Date</h3>
                <p class="text-gray-600">
                    {% if task.due_date %}{{ task.due_date.strftime('%Y-%m-%d %H:%M') }}{% else %}No due date set{% endif %}
                </p>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Created</h3>
                <p class="text-gray-600">{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
            </div>
        </div>
        
        {% if task.description %}
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Description</h3>
            <div class="bg-gray-50 p-4 rounded-md">
                <p class="text-gray-700 whitespace-pre-wrap">{{ task.description }}</p>
            </div>
        </div>
        {% endif %}
        
        <div class="border-t pt-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Self-Prompts</h3>
            <a href="{{ url_for('reminders.new_reminder', task_id=task.id) }}" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">Add Self-Prompt</a>
        </div>
    </div>
</div>
{% endblock %}
