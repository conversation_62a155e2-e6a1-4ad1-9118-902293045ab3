{% extends "layout.html" %}

{% block page_title %}Tasks{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Tasks</h1>
        <a href="{{ url_for('tasks.new_task', project_id=request.args.get('project_id')) }}" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">New Task</a>
    </div>
    
    {% if tasks %}
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for task in tasks %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ task.name }}</div>
                            <div class="text-sm text-gray-500">{{ task.description[:50] }}{% if task.description|length > 50 %}...{% endif %}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if task.status == 'completed' %}bg-green-100 text-green-800
                                {% elif task.status == 'in_progress' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ task.status.replace('_', ' ').title() }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ task.priority.title() }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if task.due_date %}{{ task.due_date.strftime('%Y-%m-%d %H:%M') }}{% else %}No due date{% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{{ url_for('tasks.task_detail', task_id=task.id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">View</a>
                            <a href="{{ url_for('tasks.edit_task', task_id=task.id) }}" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="text-center py-12">
            <p class="text-gray-500 text-lg">No tasks found.</p>
            <a href="{{ url_for('tasks.new_task', project_id=request.args.get('project_id')) }}" class="mt-4 inline-block px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">Create your first task</a>
        </div>
    {% endif %}
</div>
{% endblock %}
