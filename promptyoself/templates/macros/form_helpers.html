{% macro render_field(field, label_visible=true) -%}
  {% set field_type = field.type|lower %}
  {% set default_classes = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %}
  {% if field_type == 'submitfield' %}
    {% set default_classes = "px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %}
  {% elif field_type == 'booleanfield' %}
    {% set default_classes = "h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" %}
  {% endif %}

  <div class="mb-4 {{ kwargs.pop('class_', '') }}">
    {% if label_visible and field_type != 'submitfield' and field_type != 'csrfhiddenfield' and field_type != 'hiddenfield' %}
      {{ field.label(class="block text-sm font-medium text-gray-700 mb-1") }}
    {% endif %}
    
    {% if field_type == 'booleanfield' %}
      <div class="flex items-center">
        {{ field(class=default_classes ~ " " ~ kwargs.pop('class', '')) }}
        {% if label_visible %}
          <label for="{{ field.id }}" class="ml-2 block text-sm text-gray-900">{{ field.label.text }}</label>
        {% endif %}
      </div>
    {% elif field_type != 'csrfhiddenfield' and field_type != 'hiddenfield' %}
      {{ field(class=default_classes ~ " " ~ kwargs.pop('class', ''), **kwargs) }}
    {% else %}
       {{ field(**kwargs) }} {# For hidden fields, no default styling #}
    {% endif %}

    {% if field.errors %}
      <div class="mt-1">
      {% for error in field.errors %}
        <p class="text-xs text-red-600">{{ error }}</p>
      {% endfor %}
      </div>
    {% endif %}
    
    {% if field.description and field_type != 'submitfield' and field_type != 'csrfhiddenfield' and field_type != 'hiddenfield' %}
        <p class="mt-2 text-sm text-gray-500">{{ field.description }}</p>
    {% endif %}
  </div>
{%- endmacro %}