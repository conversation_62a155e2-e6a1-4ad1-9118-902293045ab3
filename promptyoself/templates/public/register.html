
{% extends "layout.html" %}
{% from "macros/form_helpers.html" import render_field %}

{% block content %}
<div class="min-h-screen flex flex-col items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8 bg-white p-10 rounded-xl shadow-lg">
    <div>
      <h1 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Create your account
      </h1>
    </div>
    <form id="registerForm" class="mt-8 space-y-6" method="POST" action="{{ url_for('public.register') }}" role="form">
      {{ form.hidden_tag() }} {# Includes csrf_token #}
      
      {{ render_field(form.username, placeholder="Username") }}
      {{ render_field(form.email, placeholder="Email address", description="We'll never share your email with anyone else.") }}
      {{ render_field(form.password, placeholder="Password", type="password") }}
      {{ render_field(form.confirm, placeholder="Confirm Password", type="password") }}

      <div>
        <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          Register
        </button>
      </div>
    </form>
    <p class="mt-2 text-center text-sm text-gray-600">
      Already registered?
      <a href="{{ url_for('public.home') }}" class="font-medium text-indigo-600 hover:text-indigo-500">
        Login here
      </a>
    </p>
  </div>
</div>
{% endblock %}

