{% extends "layout.html" %}

{% block page_title %}Reminder Details: {{ reminder.message|truncate(30) }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <a href="{{ url_for('reminders.list_reminders') }}" class="text-indigo-600 hover:text-indigo-800 text-sm">&larr; Back to Reminders List</a>
    </div>
    <h1 class="text-3xl font-bold text-gray-800 mb-6 break-words">Reminder: {{ reminder.message }}</h1>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-700">Reminder Information</h2>
        </div>
        <div class="px-6 py-4">
            <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">ID</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ reminder.id }}</dd>
                </div>
                 <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Message</dt>
                    <dd class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ reminder.message }}</dd>
                </div>

                {% if reminder.task %}
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Associated Task</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <a href="{{ url_for('tasks.view_task', task_id=reminder.task.id) }}" class="text-indigo-600 hover:text-indigo-800">{{ reminder.task.name }}</a>
                    </dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Associated Project</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <a href="{{ url_for('projects.view_project', project_id=reminder.task.project.id) }}" class="text-indigo-600 hover:text-indigo-800">{{ reminder.task.project.name }}</a>
                    </dd>
                </div>
                {% else %}
                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Associated Task</dt>
                    <dd class="mt-1 text-sm text-gray-900">N/A</dd>
                </div>
                {% endif %}
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Next Run Time</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ reminder.next_run.strftime('%Y-%m-%d %H:%M:%S') if reminder.next_run else 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Recurrence</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ reminder.recurrence or 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Process Name</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ reminder.process_name or 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Created At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ reminder.created_at.strftime('%Y-%m-%d %H:%M') if reminder.created_at else 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Updated At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ reminder.updated_at.strftime('%Y-%m-%d %H:%M') if reminder.updated_at else 'N/A' }}</dd>
                </div>
            </dl>
        </div>
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 justify-end">
            <a href="{{ url_for('reminders.edit_reminder', reminder_id=reminder.id) }}" class="px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 text-center">Edit Reminder</a>
            <form action="{{ url_for('reminders.delete_reminder', reminder_id=reminder.id) }}" method="POST" class="inline-block">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <button type="submit" class="w-full sm:w-auto px-4 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this reminder?');">Delete Reminder</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}