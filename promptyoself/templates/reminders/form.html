{% extends "layout.html" %}
{% from "macros/form_helpers.html" import render_field %}

{% block page_title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-2xl">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">{{ title }}</h1>
    <form method="POST" action="{{ request.path }}" class="bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4" role="form">
        {{ form.hidden_tag() }}
        {{ render_field(form.task_id) }}
        {{ render_field(form.message, placeholder="Enter reminder message", rows=3) }}
        {{ render_field(form.next_run, type="datetime-local") }}
        {{ render_field(form.recurrence, placeholder="e.g., daily, weekly, cron: 0 9 * * MON") }}
        {{ render_field(form.process_name, placeholder="Enter process name (e.g., email_notification)") }}
        
        <div class="flex items-center justify-end mt-6 space-x-3">
            <a href="{{ url_for('reminders.list_reminders', task_id=form.task_id.data if form.task_id.data else None) }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
            <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">Save Reminder</button>
        </div>
    </form>
</div>
{% endblock %}
