{% extends "layout.html" %}

{% block page_title %}Reminders{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-4 md:mb-0">Reminders</h1>
        <a href="{{ url_for('reminders.new_reminder') }}" class="w-full md:w-auto px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-center md:ml-auto">New Reminder</a>
    </div>

    <form method="GET" action="{{ url_for('reminders.list_reminders') }}" class="mb-6 bg-white p-4 rounded-lg shadow">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
                <label for="process_name_filter" class="block text-sm font-medium text-gray-700">Filter by process name</label>
                <input type="text" name="process_name" id="process_name_filter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Process name" value="{{ process_name_filter or '' }}">
            </div>
            <div>
                <label for="task_id_filter" class="block text-sm font-medium text-gray-700">Task</label>
                <select name="task_id" id="task_id_filter" class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="">All Tasks</option>
                    {% for t in tasks %}
                    <option value="{{ t.id }}" {% if task_id_filter|int == t.id %}selected{% endif %}>{{ t.project.name }} - {{ t.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <button type="submit" class="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">Filter</button>
        </div>
    </form>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200" role="table" aria-label="Reminders list">
            <thead class="bg-gray-50" role="rowgroup">
                <tr role="row">
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Run</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recurrence</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Process Name</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" role="rowgroup">
                {% for reminder in paginated_reminders.items %}
                <tr role="row">
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <a href="{{ url_for('reminders.view_reminder', reminder_id=reminder.id) }}" class="text-indigo-600 hover:text-indigo-900">{{ reminder.message|truncate(50) }}</a>
                    </td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ reminder.task.name if reminder.task else 'N/A' }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ reminder.task.project.name if reminder.task and reminder.task.project else 'N/A' }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ reminder.next_run.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ reminder.recurrence or 'N/A' }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ reminder.process_name }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <a href="{{ url_for('reminders.edit_reminder', reminder_id=reminder.id) }}" class="px-3 py-1 text-xs text-white bg-blue-500 rounded-md hover:bg-blue-600">Edit</a>
                        <form action="{{ url_for('reminders.delete_reminder', reminder_id=reminder.id) }}" method="POST" class="inline-block">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="px-3 py-1 text-xs text-white bg-red-500 rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this reminder?');">Delete</button>
                        </form>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No reminders found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if paginated_reminders.pages > 1 %}
    <nav class="mt-6" aria-label="Reminders pagination">
        <ul class="flex justify-center items-center space-x-1" role="list">
            {% if paginated_reminders.has_prev %}
                <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('reminders.list_reminders', page=paginated_reminders.prev_num, process_name=process_name_filter, task_id=task_id_filter) }}">Previous</a></li>
            {% else %}
                <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-400 bg-gray-50 border border-gray-300 rounded-l-lg cursor-not-allowed">Previous</span></li>
            {% endif %}
    
            {% for page_num in paginated_reminders.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                {% if page_num %}
                    {% if paginated_reminders.page == page_num %}
                        <li role="listitem"><span class="px-3 py-2 leading-tight text-indigo-600 bg-indigo-50 border border-indigo-300 hover:bg-indigo-100 hover:text-indigo-700 z-10" aria-current="page">{{ page_num }}</span></li>
                    {% else %}
                        <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('reminders.list_reminders', page=page_num, process_name=process_name_filter, task_id=task_id_filter) }}">{{ page_num }}</a></li>
                    {% endif %}
                {% else %}
                     <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300">...</span></li>
                {% endif %}
            {% endfor %}
    
            {% if paginated_reminders.has_next %}
                <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('reminders.list_reminders', page=paginated_reminders.next_num, process_name=process_name_filter, task_id=task_id_filter) }}">Next</a></li>
            {% else %}
                <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-400 bg-gray-50 border border-gray-300 rounded-r-lg cursor-not-allowed">Next</span></li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}
