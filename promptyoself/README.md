# PromptYoSelf v3

An app to help computational beings prompt themselves independently through scheduled self-prompts.

## Docker Quickstart

This app can be run completely using `Docker` and `docker compose`. **Using Docker is recommended, as it guarantees the application is run using compatible versions of Python and Node**.

There are three main services:

To run the development version of the app

```bash
docker compose up flask-dev
```

To run the production version of the app

```bash
docker compose up flask-prod
```

The list of `environment:` variables in the `docker compose.yml` file takes precedence over any variables specified in `.env`.

To run any commands using the `Flask CLI`

```bash
docker compose run --rm manage <<COMMAND>>
```

Therefore, to initialize a database you would run

```bash
docker compose run --rm manage db init
docker compose run --rm manage db migrate
docker compose run --rm manage db upgrade
```

A docker volume `node-modules` is created to store NPM packages and is reused across the dev and prod versions of the application. For the purposes of DB testing with `sqlite`, the file `dev.db` is mounted to all containers. This volume mount should be removed from `docker compose.yml` if a production DB server is used.

Go to `http://localhost:8080`. You will see a pretty welcome screen.

### Running locally

Run the following commands to bootstrap your environment if you are unable to run the application using Docker

```bash
cd promptyoself
pip install -r requirements/dev.txt
npm install
npm run-script build
npm start  # run the webpack dev server and flask server using concurrently
```

Go to `http://localhost:5000`. You will see a pretty welcome screen.

#### Database Initialization (locally)

Once you have installed your DBMS, run the following to create your app's
database tables and perform the initial migration

```bash
flask db init
flask db migrate
flask db upgrade
```

## Deployment

When using Docker, reasonable production defaults are set in `docker compose.yml`

```text
FLASK_ENV=production
FLASK_DEBUG=0
```

Therefore, starting the app in "production" mode is as simple as

```bash
docker compose up flask-prod
```

If running without Docker

```bash
export FLASK_ENV=production
export FLASK_DEBUG=0
export DATABASE_URL="<YOUR DATABASE URL>"
npm run build   # build assets with webpack
flask run       # start the flask server
```

## Shell

To open the interactive shell, run

```bash
docker compose run --rm manage shell
flask shell # If running locally without Docker
```

By default, you will have access to the flask `app`.

## Running Tests/Linter

To run all tests, run

```bash
docker compose run --rm manage test
flask test # If running locally without Docker
```

To run the linter, run

```bash
docker compose run --rm manage lint
flask lint # If running locally without Docker
```

The `lint` command will attempt to fix any linting/style errors in the code. If you only want to know if the code will pass CI and do not wish for the linter to make changes, add the `--check` argument.

## Migrations

Whenever a database migration needs to be made. Run the following commands

```bash
docker compose run --rm manage db migrate
flask db migrate # If running locally without Docker
```

This will generate a new migration script. Then run

```bash
docker compose run --rm manage db upgrade
flask db upgrade # If running locally without Docker
```

To apply the migration.

For a full migration command reference, run `docker compose run --rm manage db --help`.

If you will deploy your application remotely (e.g on Heroku) you should add the `migrations` folder to version control.
You can do this after `flask db migrate` by running the following commands

```bash
git add migrations/*
git commit -m "Add migrations"
```

Make sure folder `migrations/versions` is not empty.

## API Documentation

This application provides API endpoints for both internal and external use. Internal endpoints are primarily designed for communication between managed agents and the application.

### Internal API Endpoints

#### POST /api/internal/agents/reminders

This endpoint allows trusted agents (e.g., Letta) to programmatically schedule reminders through the internal API.

**Authentication:**
- Required: API Key via `X-Agent-API-Key` header
- The API key must match the `INTERNAL_AGENT_API_KEY` environment variable

**Request Body (JSON):**
```json
{
  "agent_id": "string",           // ID of the agent scheduling the reminder
  "reminder_text": "string",      // Text content of the reminder (required, non-empty)
  "scheduled_for": "string",      // ISO 8601 formatted datetime when reminder should trigger
  "process_name": "string"        // Process name (required, non-empty)
}
```

**Example Request:**
```bash
curl -X POST \
  http://localhost:5000/api/internal/agents/self-prompts \
  -H 'Content-Type: application/json' \
  -H 'X-Agent-API-Key: your_api_key_here' \
  -d '{
    "agent_id": "letta_agent_1",
    "prompt_text": "Check system status and report back",
    "scheduled_for": "2025-06-15T14:30:00Z",
    "process_name": "system_monitor"
  }'
```

**Success Response (201 Created):**
```json
{
  "message": "Self-prompt created successfully",
  "self_prompt": {
    "id": 123,
    "agent_id": "letta_agent_1",
    "message": "Check system status and report back",
    "scheduled_for": "2025-06-15T14:30:00+00:00",
    "process_name": "system_monitor",
    "status": "pending"
  }
}
```

**Error Responses:**

- **400 Bad Request** - Missing or invalid request fields:
  ```json
  {
    "error": "Missing required field: reminder_text"
  }
  ```

- **401 Unauthorized** - Missing or invalid API key:
  ```json
  {
    "error": "Authentication required - missing API key"
  }
  ```

- **409 Conflict** - Database constraint violation:
  ```json
  {
    "error": "Database constraint violation. This could be due to a duplicate entry or invalid reference."
  }
  ```

- **422 Unprocessable Entity** - Data type error:
  ```json
  {
    "error": "Invalid data for database. The provided values don't match the expected data types."
  }
  ```

- **503 Service Unavailable** - Database unavailable:
  ```json
  {
    "error": "Database operation failed. The service may be temporarily unavailable."
  }
  ```

- **500 Internal Server Error** - Unexpected error:
  ```json
  {
    "error": "An unexpected error occurred while processing your request."
  }
  ```

## Data Models

This application uses SQLAlchemy models to represent core entities. Key architectural decisions regarding user identification:

### User Identification (user_id)

The application uses a consistent approach for user identification:

- **Primary Key as User ID**: The `User` model inherits an auto-incrementing primary key `id` field from `PkModel` that serves as the unique user identifier throughout the application.
- **No Separate user_id Field**: Rather than maintaining a separate `user_id` field, the inherited `id` field fulfills this role, simplifying the data model and avoiding redundancy.
- **Session Management**: When working with authenticated users, `current_user.id` provides access to the user's unique identifier.
- **API and Relationships**: All foreign key references to users (e.g., in roles, project ownership) use the `User.id` field as the user identifier.

This design ensures consistency across the application while leveraging SQLAlchemy's built-in primary key functionality.

## Asset Management

Files placed inside the `assets` directory and its subdirectories
(excluding `js` and `css`) will be copied by webpack's
`file-loader` into the `static/build` directory. In production, the plugin
`Flask-Static-Digest` zips the webpack content and tags them with a MD5 hash.
As a result, you must use the `static_url_for` function when including static content,
as it resolves the correct file name, including the MD5 hash.
For example

```html
<link rel="shortcut icon" href="{{static_url_for('static', filename='build/favicon.ico') }}">
```

If all of your static files are managed this way, then their filenames will change whenever their
contents do, and you can ask Flask to tell web browsers that they
should cache all your assets forever by including the following line
in ``.env``:

```text
SEND_FILE_MAX_AGE_DEFAULT=31556926  # one year
```
