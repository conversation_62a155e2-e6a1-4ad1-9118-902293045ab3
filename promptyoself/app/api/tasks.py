# -*- coding: utf-8 -*-
"""API endpoints for tasks."""
from flask import Blueprint, request, jsonify, current_app
from werkzeug.exceptions import BadRequest, NotFound

from app.forms import TaskForm
from app.models import Task, Project
from app.extensions import db, limiter  # Added limiter

blueprint = Blueprint("api_tasks", __name__, url_prefix="/api/tasks")


@blueprint.route("/", methods=["GET"])
@limiter.limit("100 per hour")  # Added rate limit
def list_tasks():
    """List all tasks via API with pagination and filtering."""
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)
    name_filter = request.args.get("name", None, type=str)
    project_id_filter = request.args.get("project_id", None, type=int)

    query = Task.query.join(Project)
    if name_filter:
        query = query.filter(Task.name.ilike(f"%{name_filter}%"))
    if project_id_filter:
        query = query.filter(Task.project_id == project_id_filter)

    paginated_tasks = query.order_by(Project.name, Task.name).paginate(
        page=page, per_page=per_page, error_out=False)
    tasks = paginated_tasks.items

    return jsonify({
        "tasks": [
            {
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "project_id": task.project_id,
                "project_name": task.project.name if task.project else None,
                "parent_task_id": task.parent_task_id,
                "parent_task_name": task.parent.name if task.parent else None,
                "created_at": task.created_at.isoformat(),
                "updated_at": task.updated_at.isoformat(),
            }
            for task in tasks
        ],
        "pagination": {
            "page": paginated_tasks.page,
            "per_page": paginated_tasks.per_page,
            "total_pages": paginated_tasks.pages,
            "total_items": paginated_tasks.total,
            "has_prev": paginated_tasks.has_prev,
            "has_next": paginated_tasks.has_next,
            "prev_num": paginated_tasks.prev_num,
            "next_num": paginated_tasks.next_num,
        }
    }), 200


@blueprint.route("/", methods=["POST"])
@limiter.limit("50 per hour")  # Added rate limit
def create_task():
    """Create a new task via API."""
    if not request.is_json:
        raise BadRequest("Content-Type must be application/json")

    data = request.get_json()
    form = TaskForm(data=data)

    if form.validate():
        task = Task.create(
            name=form.name.data,
            description=form.description.data,
            project_id=form.project_id.data,
            parent_task_id=form.parent_task_id.data or None
        )
        current_app.logger.info(f"Task created via API: {task.name}")

        return jsonify({
            "message": "Task created successfully",
            "task": {
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "project_id": task.project_id,
                "project_name": task.project.name if task.project else None,
                "parent_task_id": task.parent_task_id,
                "parent_task_name": task.parent.name if task.parent else None,
                "created_at": task.created_at.isoformat(),
                "updated_at": task.updated_at.isoformat(),
            }
        }), 201
    else:
        return jsonify({"errors": form.errors}), 400


@blueprint.route("/<int:task_id>", methods=["GET"])
@limiter.limit("100 per hour")  # Added rate limit
def get_task(task_id):
    """Get a specific task via API."""
    task = Task.query.get(task_id)

    if not task:
        raise NotFound("Task not found")

    return jsonify({
        "task": {
            "id": task.id,
            "name": task.name,
            "description": task.description,
            "project_id": task.project_id,
            "project_name": task.project.name if task.project else None,
            "parent_task_id": task.parent_task_id,
            "parent_task_name": task.parent.name if task.parent else None,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat(),
        }
    }), 200


@blueprint.route("/<int:task_id>", methods=["PUT"])
@limiter.limit("50 per hour")  # Added rate limit
def update_task(task_id):
    """Update a task via API."""
    if not request.is_json:
        raise BadRequest("Content-Type must be application/json")

    task = Task.query.get(task_id)
    if not task:
        raise NotFound("Task not found")

    data = request.get_json()
    form = TaskForm(data=data, obj=task)

    if form.validate():
        form.populate_obj(task)
        db.session.commit()
        current_app.logger.info(f"Task updated via API: {task.name}")

        return jsonify({
            "message": "Task updated successfully",
            "task": {
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "project_id": task.project_id,
                "project_name": task.project.name if task.project else None,
                "parent_task_id": task.parent_task_id,
                "parent_task_name": task.parent.name if task.parent else None,
                "created_at": task.created_at.isoformat(),
                "updated_at": task.updated_at.isoformat(),
            }
        }), 200
    else:
        return jsonify({"errors": form.errors}), 400


@blueprint.route("/<int:task_id>", methods=["DELETE"])
@limiter.limit("50 per hour")  # Added rate limit
def delete_task(task_id):
    """Delete a task via API."""
    task = Task.query.get(task_id)
    if not task:
        raise NotFound("Task not found")

    name = task.name
    db.session.delete(task)
    db.session.commit()
    current_app.logger.info(f"Task deleted via API: {name}")

    return jsonify({"message": f"Task '{name}' deleted successfully"}), 200
