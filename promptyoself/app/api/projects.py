# -*- coding: utf-8 -*-
"""API endpoints for projects."""
from flask import Blueprint, request, jsonify, current_app
from werkzeug.exceptions import BadRequest, NotFound

from app.forms import ProjectForm
from app.models import Project
from app.extensions import db, limiter  # Added limiter

blueprint = Blueprint("api_projects", __name__, url_prefix="/api/projects")


@blueprint.route("/", methods=["GET"])
@limiter.limit("100 per hour")  # Added rate limit
def list_projects():
    """List all projects via API with pagination and filtering."""
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)
    name_filter = request.args.get("name", None, type=str)

    query = Project.query
    if name_filter:
        query = query.filter(Project.name.ilike(f"%{name_filter}%"))

    paginated_projects = query.paginate(
        page=page, per_page=per_page, error_out=False)
    projects = paginated_projects.items

    return jsonify({
        "projects": [
            {
                "id": project.id,
                "name": project.name,
                "description": project.description,
                "created_at": project.created_at.isoformat(),
                "updated_at": project.updated_at.isoformat(),
            }
            for project in projects
        ],
        "pagination": {
            "page": paginated_projects.page,
            "per_page": paginated_projects.per_page,
            "total_pages": paginated_projects.pages,
            "total_items": paginated_projects.total,
            "has_prev": paginated_projects.has_prev,
            "has_next": paginated_projects.has_next,
            "prev_num": paginated_projects.prev_num,
            "next_num": paginated_projects.next_num,
        }
    }), 200


@blueprint.route("/", methods=["POST"])
@limiter.limit("50 per hour")  # Added rate limit
def create_project():
    """Create a new project via API."""
    if not request.is_json:
        raise BadRequest("Content-Type must be application/json")

    data = request.get_json()
    form = ProjectForm(data=data)

    if form.validate():
        project = Project.create(
            name=form.name.data,
            description=form.description.data,
        )
        current_app.logger.info(f"Project created via API: {project.name}")

        return jsonify({
            "message": "Project created successfully",
            "project": {
                "id": project.id,
                "name": project.name,
                "description": project.description,
                "created_at": project.created_at.isoformat(),
                "updated_at": project.updated_at.isoformat(),
            }
        }), 201
    else:
        return jsonify({"errors": form.errors}), 400


@blueprint.route("/<int:project_id>", methods=["GET"])
@limiter.limit("100 per hour")  # Added rate limit
def get_project(project_id):
    """Get a specific project via API."""
    project = Project.query.get(project_id)

    if not project:
        raise NotFound("Project not found")

    return jsonify({
        "project": {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "created_at": project.created_at.isoformat(),
            "updated_at": project.updated_at.isoformat(),
        }
    }), 200


@blueprint.route("/<int:project_id>", methods=["PUT"])
@limiter.limit("50 per hour")  # Added rate limit
def update_project(project_id):
    """Update a project via API."""
    if not request.is_json:
        raise BadRequest("Content-Type must be application/json")

    project = Project.query.get(project_id)
    if not project:
        raise NotFound("Project not found")

    data = request.get_json()
    form = ProjectForm(data=data, obj=project)

    if form.validate():
        form.populate_obj(project)
        db.session.commit()
        current_app.logger.info(f"Project updated via API: {project.name}")

        return jsonify({
            "message": "Project updated successfully",
            "project": {
                "id": project.id,
                "name": project.name,
                "description": project.description,
                "created_at": project.created_at.isoformat(),
                "updated_at": project.updated_at.isoformat(),
            }
        }), 200
    else:
        return jsonify({"errors": form.errors}), 400


@blueprint.route("/<int:project_id>", methods=["DELETE"])
@limiter.limit("50 per hour")  # Added rate limit
def delete_project(project_id):
    """Delete a project via API."""
    project = Project.query.get(project_id)
    if not project:
        raise NotFound("Project not found")

    name = project.name
    db.session.delete(project)
    db.session.commit()
    current_app.logger.info(f"Project deleted via API: {name}")

    return jsonify({"message": f"Project '{name}' deleted successfully"}), 200
