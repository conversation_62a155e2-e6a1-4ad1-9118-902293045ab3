import json
import uuid
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def create_jsonrpc_request(method: str, params: Optional[Dict[str, Any]] = None, message_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Creates a JSON-RPC request object.

    Args:
        method: The name of the method to be invoked.
        params: A structured value that holds the parameter values to be used
                during the invocation of the method. This member MAY be omitted.
        message_id: An identifier established by the Client that MUST contain a String,
                    Number, or NULL value if included. If it is not included it is
                    assumed to be a notification. The value SHOULD normally not be
                    Null and Numbers SHOULD NOT contain fractional parts.

    Returns:
        A dictionary representing the JSON-RPC request.
    """
    actual_message_id: Any = message_id if message_id is not None else str(
        uuid.uuid4())

    request: Dict[str, Any] = {
        "jsonrpc": "2.0",
        "method": method,
        "id": actual_message_id
    }
    if params is not None:
        request["params"] = params
    return request


def serialize_jsonrpc_message(message: Dict[str, Any]) -> str:
    """
    Serializes a JSON-RPC message object to a JSON string.

    Args:
        message: The JSON-RPC message object (request or response).

    Returns:
        A JSON string representation of the message.
    """
    try:
        return json.dumps(message)
    except TypeError as e:
        logger.error(f"Error serializing JSON-RPC message: {e}")
        # Consider re-raising a custom exception or returning a specific error structure
        raise


def deserialize_jsonrpc_message(message_str: str) -> Dict[str, Any]:
    """
    Deserializes a JSON string to a JSON-RPC message object.

    Args:
        message_str: The JSON string representation of the message.

    Returns:
        A dictionary representing the JSON-RPC message.
    """
    try:
        return json.loads(message_str)
    except json.JSONDecodeError as e:
        logger.error(f"Error deserializing JSON-RPC message string: {e}")
        raise


if __name__ == '__main__':
    # Example Usage
    request_obj = create_jsonrpc_request(
        "echo", {"message": "Hello, world!"}, "1")
    print(f"Request Object: {request_obj}")
    serialized_request = serialize_jsonrpc_message(request_obj)
    print(f"Serialized Request: {serialized_request}")

    notification_obj = create_jsonrpc_request(
        "notify_event", {"event": "server_started"})
    print(f"Notification Object: {notification_obj}")
    serialized_notification = serialize_jsonrpc_message(notification_obj)
    print(f"Serialized Notification: {serialized_notification}")

    # Example of deserialization (assuming we received a response string)
    response_str = '{"jsonrpc": "2.0", "result": "Hello, world!", "id": "1"}'
    deserialized_response = deserialize_jsonrpc_message(response_str)
    print(f"Deserialized Response: {deserialized_response}")

    error_response_str = '{"jsonrpc": "2.0", "error": {"code": -32600, "message": "Invalid Request"}, "id": null}'
    deserialized_error_response = deserialize_jsonrpc_message(
        error_response_str)
    print(f"Deserialized Error Response: {deserialized_error_response}")
