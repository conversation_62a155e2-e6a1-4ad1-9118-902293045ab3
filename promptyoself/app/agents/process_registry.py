import logging
import threading
import subprocess
from typing import Dict, Optional, List, TypedDict  # Removed Any

logger = logging.getLogger(__name__)


class ProcessDetails(TypedDict):
    name: str  # Added name
    popen_object: subprocess.Popen[str]
    pid: int
    status: str
    command: List[str]


class ProcessRegistry:
    """
    Manages registration, lookup, and removal of managed subprocesses.
    Ensures thread-safe access to process information.
    """

    def __init__(self):
        self._processes: Dict[str, ProcessDetails] = {}
        self._lock = threading.Lock()
        logger.info("ProcessRegistry initialized.")

    # Changed Popen[Any] to Popen[str]
    def register_process(self, name: str, popen_object: subprocess.Popen[str], command: List[str]) -> bool:
        """
        Registers a new process.

        Args:
            name: A unique name for the process.
            popen_object: The subprocess.Popen object for the process.
            command: The command list used to start the process.

        Returns:
            True if registration was successful, False if a process with the same name already exists.
        """
        with self._lock:
            if name in self._processes:
                logger.warning(
                    f"Attempted to register process with duplicate name: {name}")
                return False

            self._processes[name] = ProcessDetails(
                name=name,  # Store name within the ProcessDetails dict
                popen_object=popen_object,
                pid=popen_object.pid,
                status="running",
                command=command
            )
            logger.info(
                f"Process '{name}' (PID: {popen_object.pid}) registered with command: {' '.join(command)}.")
            return True

    def get_process(self, name: str) -> Optional[ProcessDetails]:
        """
        Retrieves information about a registered process.

        Args:
            name: The name of the process to retrieve.

        Returns:
            A ProcessDetails dictionary containing process information if found, otherwise None.
        """
        with self._lock:
            process_info = self._processes.get(name)
            if process_info:
                # Update status if possible
                # Ensure popen_object is not None before calling poll
                popen = process_info.get("popen_object")
                if popen and popen.poll() is not None:
                    process_info["status"] = "terminated"
                logger.debug(f"Retrieved process '{name}'.")
                # Return a copy to prevent external modification of the TypedDict
                return process_info.copy() if process_info else None
            else:
                logger.debug(
                    f"Attempted to retrieve non-existent process: {name}")
                return None

    def unregister_process(self, name: str) -> bool:
        """
        Removes a process from the registry.
        This method does not terminate the process, only removes its record.
        Termination should be handled separately if needed.

        Args:
            name: The name of the process to unregister.

        Returns:
            True if the process was found and unregistered, False otherwise.
        """
        with self._lock:
            if name in self._processes:
                pid = self._processes[name].get("pid", "N/A")
                del self._processes[name]
                logger.info(f"Process '{name}' (PID: {pid}) unregistered.")
                return True
            else:
                logger.warning(
                    f"Attempted to unregister non-existent process: {name}")
                return False

    def list_processes(self) -> List[ProcessDetails]:
        """
        Lists all registered processes with their current information.

        Returns:
            A list of ProcessDetails dictionaries, where each dictionary contains information
            about a registered process.
        """
        with self._lock:
            process_list: List[ProcessDetails] = []
            # Iterate through the values (ProcessDetails objects) directly
            for process_details in self._processes.values():
                # Update status before listing
                popen = process_details.get("popen_object")
                if popen and popen.poll() is not None:
                    # Update the status in the actual stored dictionary
                    if process_details["status"] != "terminated":
                        process_details["status"] = "terminated"
                        logger.info(
                            f"Process '{process_details['name']}' (PID: {process_details['pid']}) detected as terminated during listing, status updated in registry.")
                # Append a copy of the ProcessDetails dictionary
                process_list.append(process_details.copy())
            logger.debug(f"Listed {len(process_list)} processes.")
            return process_list

    def is_registered(self, name: str) -> bool:
        """
        Checks if a process with the given name is registered.

        Args:
            name: The name of the process to check.

        Returns:
            True if the process is registered, False otherwise.
        """
        with self._lock:
            return name in self._processes

    def update_process_status(self, name: str, status: str) -> bool:
        """
        Updates the status of a registered process.

        Args:
            name: The name of the process.
            status: The new status string.

        Returns:
            True if the process was found and status updated, False otherwise.
        """
        with self._lock:
            if name in self._processes:
                self._processes[name]["status"] = status
                logger.info(
                    f"Status for process '{name}' updated to '{status}'.")
                return True
            logger.warning(
                f"Attempted to update status for non-existent process: {name}")
            return False


if __name__ == '__main__':
    # Basic example usage and test
    logging.basicConfig(level=logging.DEBUG)
    registry = ProcessRegistry()

    # Example: Registering a dummy process (e.g., sleep)
    try:
        # For Windows, use ['cmd', '/c', 'timeout', '/t', '5']
        # For Linux/macOS, use ['sleep', '5']
        import platform
        dummy_command_list: List[str] = []
        proc1: Optional[subprocess.Popen[str]] = None
        proc2: Optional[subprocess.Popen[str]] = None

        if platform.system() == "Windows":
            dummy_command_list = ['cmd', '/c', 'timeout',
                                  '/t', '2', '>nul']
        else:
            dummy_command_list = ['sleep', '2']

        if dummy_command_list:
            # Create Popen with text=True for Popen[str]
            proc1 = subprocess.Popen(dummy_command_list, text=True)
            registry.register_process(
                "dummy_process_1", proc1, dummy_command_list)

            proc2 = subprocess.Popen(dummy_command_list, text=True)
            registry.register_process(
                "dummy_process_2", proc2, dummy_command_list)
        else:
            logger.error(
                "Dummy command list is empty, skipping dummy process registration.")

    except FileNotFoundError:
        logger.error(
            "Could not find 'sleep' or 'timeout' command for testing. Skipping dummy process registration.")

    logger.info(
        f"Is 'dummy_process_1' registered? {registry.is_registered('dummy_process_1')}")

    process_info_retrieved = registry.get_process(
        "dummy_process_1")  # Renamed to avoid conflict
    if process_info_retrieved:
        logger.info(
            f"Process 'dummy_process_1' info: {process_info_retrieved}")

    logger.info("Listing all processes:")
    for p_info_item in registry.list_processes():  # Renamed to avoid conflict
        logger.info(p_info_item)

    registry.update_process_status(
        "dummy_process_1", "manually_updated_status")
    logger.info(
        f"Process 'dummy_process_1' after status update: {registry.get_process('dummy_process_1')}")

    # Wait for a bit for processes to potentially finish
    try:
        if proc1:  # Check if proc1 was assigned
            proc1.wait(timeout=5)
        if proc2:  # Check if proc2 was assigned
            proc2.wait(timeout=5)
    except subprocess.TimeoutExpired:
        logger.warning("Dummy processes did not terminate in time.")
    # NameError should not occur now with pre-initialization to None

    logger.info("Listing processes after waiting:")
    for p_info_item_after_wait in registry.list_processes():  # Renamed
        logger.info(p_info_item_after_wait)

    registry.unregister_process("dummy_process_1")
    logger.info(
        f"Is 'dummy_process_1' registered after unregister? {registry.is_registered('dummy_process_1')}")

    logger.info("Listing processes after unregistering one:")
    for p_info_item_after_unregister in registry.list_processes():  # Renamed
        logger.info(p_info_item_after_unregister)

    # Clean up any remaining processes
    # list_processes now returns List[ProcessDetails], and ProcessDetails includes 'name'
    for p_info_final_item in registry.list_processes():  # Renamed
        # p_info_final_item is a ProcessDetails dict which includes 'name'
        process_name = p_info_final_item['name']
        # We can use get_process to get the full object again if needed, or use p_info_final_item directly
        popen = p_info_final_item.get('popen_object')

        if popen and popen.poll() is None:  # if still running
            logger.info(
                f"Terminating process {process_name} (PID: {p_info_final_item['pid']})")
            popen.terminate()
            try:
                popen.wait(timeout=1)
            except subprocess.TimeoutExpired:
                logger.warning(
                    f"Process {process_name} did not terminate gracefully, killing.")
                popen.kill()
                popen.wait()
        # Unregister after attempting termination
        registry.unregister_process(process_name)

    logger.info("Final list of processes (should be empty):")
    logger.info(registry.list_processes())
