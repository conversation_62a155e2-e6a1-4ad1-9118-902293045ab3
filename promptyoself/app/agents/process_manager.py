import time
import json
import logging
import threading
import subprocess
import io  # Added for type hinting
from .process_registry import ProcessRegistry, ProcessDetails
from typing import List, Optional, Dict, Any, Union  # Added Union
from .jsonrpc_utils import (
    create_jsonrpc_request,
    serialize_jsonrpc_message,
    deserialize_jsonrpc_message,
)

logger = logging.getLogger(__name__)

# Constants for ACK handling
ACK_TIMEOUT_SECONDS = 10  # For background ACK monitoring
MAX_RETRY_ATTEMPTS = 2    # For background ACK retries
# Timeout for synchronous ACK wait in send_jsonrpc_message
ACK_SYNC_WAIT_TIMEOUT_SECONDS = 5
ACK_RESULT_VALUE = "ack"  # Expected value in a successful ACK response's 'result' field

# Constants for Agent Lifecycle Management
# Time for an agent to start and be considered "ready"
AGENT_STARTUP_TIMEOUT_SECONDS = 15
AGENT_MAX_RESTART_ATTEMPTS = 3      # Max restart attempts for a failing agent
# Period after which restart attempts for an agent are reset
AGENT_RESTART_COUNT_RESET_PERIOD_SECONDS = 3600  # 1 hour
# Interval for checking agent health (used in monitor_and_restart_agents)
AGENT_MONITOR_INTERVAL_SECONDS = 10
# Heartbeat/Ping related constants (for future enhancement)
# AGENT_HEARTBEAT_INTERVAL_SECONDS = 60
# AGENT_HEARTBEAT_TIMEOUT_SECONDS = 5


class ProcessManager:
    def __init__(self, process_registry: ProcessRegistry):
        self.process_registry = process_registry
        self.pending_acks: Dict[str, Dict[str, Any]] = {}
        self.ack_threads: Dict[str, threading.Thread] = {}
        self._stop_ack_listeners: Dict[str, threading.Event] = {}
        self._ack_monitor_thread: Optional[threading.Thread] = None
        self._stop_ack_monitor_event: threading.Event = threading.Event()
        # Type hint for agent_restart_stats: 'first_attempt_timestamp' is float
        self.agent_restart_stats: Dict[str, Dict[str, Union[int, float]]] = {}
        self._start_ack_monitor_thread()

    def _start_ack_monitor_thread(self):
        if self._ack_monitor_thread is None or not self._ack_monitor_thread.is_alive():
            self._stop_ack_monitor_event.clear()
            self._ack_monitor_thread = threading.Thread(
                target=self._check_pending_acks_periodically,
                daemon=True
            )
            self._ack_monitor_thread.start()
            logger.info("Central ACK monitor thread started.")

    def _check_pending_acks_periodically(self, check_interval_seconds: int = 1):
        logger.info(
            f"ACK monitor checking periodically every {check_interval_seconds}s.")
        while not self._stop_ack_monitor_event.is_set():
            time.sleep(check_interval_seconds)
            for message_id in list(self.pending_acks.keys()):
                if message_id not in self.pending_acks:
                    continue

                ack_details = self.pending_acks[message_id]
                agent_name = ack_details['agent_name']
                sent_timestamp = ack_details['timestamp']
                retry_count = ack_details['retry_count']

                if (time.time() - sent_timestamp) > ACK_TIMEOUT_SECONDS:
                    logger.warning(
                        f"ACK timeout for message ID '{message_id}' to agent '{agent_name}'. Retry count: {retry_count}")

                    if retry_count < MAX_RETRY_ATTEMPTS:
                        ack_details['retry_count'] += 1
                        ack_details['timestamp'] = time.time()
                        logger.info(
                            f"Retrying message ID '{message_id}' to agent '{agent_name}' (Attempt {ack_details['retry_count']}).")

                        original_message_content = ack_details.get(
                            'message_content', {})
                        method = original_message_content.get('method')
                        params = original_message_content.get('params')

                        if method:
                            resent = self.send_jsonrpc_message(
                                agent_name, method, params, message_id=message_id, is_retry=True)
                            if resent:
                                logger.info(
                                    f"Message ID '{message_id}' retransmitted to '{agent_name}'.")
                            else:
                                logger.error(
                                    f"Failed to retransmit OR get ACK for message ID '{message_id}' to '{agent_name}'. It remains in pending_acks for now.")
                        else:
                            logger.error(
                                f"Cannot retry message ID '{message_id}': original method not found in stored details.")
                            self.pending_acks.pop(message_id, None)
                    else:
                        logger.error(
                            f"Max retries ({MAX_RETRY_ATTEMPTS}) reached for message ID '{message_id}' to agent '{agent_name}'. Flagging as failed.")
                        self.pending_acks.pop(message_id, None)
        logger.info("Central ACK monitor thread stopped.")

    def _listen_for_acks(self, agent_name: str, process_stdout: io.TextIOWrapper):
        logger.info(f"Starting ACK listener thread for agent '{agent_name}'.")
        stop_event = self._stop_ack_listeners.get(agent_name)

        while stop_event and not stop_event.is_set():
            try:
                if process_stdout.closed:
                    logger.warning(
                        f"Stdout for agent '{agent_name}' is closed. Stopping ACK listener.")
                    break
                line: str = process_stdout.readline()
                if not line:
                    logger.info(
                        f"Stdout for agent '{agent_name}' ended. Stopping ACK listener.")
                    break
                line = line.strip()
                if not line:
                    continue
                logger.debug(f"Agent '{agent_name}' stdout: {line}")
                try:
                    response_obj = deserialize_jsonrpc_message(line)
                except json.JSONDecodeError:
                    logger.warning(
                        f"Non-JSON message from agent '{agent_name}': {line}")
                    continue
                message_id = response_obj.get("id")
                if message_id is None:
                    logger.debug(
                        f"Received message without ID from agent '{agent_name}': {response_obj}")
                    continue
                if message_id in self.pending_acks:
                    if "error" in response_obj:
                        error_details = response_obj["error"]
                        logger.error(
                            f"Received JSON-RPC error for message ID '{message_id}' from agent '{agent_name}': {error_details}")
                        self.pending_acks.pop(message_id, None)
                    elif "result" in response_obj and response_obj.get("result") == ACK_RESULT_VALUE:
                        logger.info(
                            f"Received ACK for message ID '{message_id}' from agent '{agent_name}'.")
                        self.pending_acks.pop(message_id, None)
                    else:
                        logger.warning(
                            f"Received unexpected JSON-RPC response for message ID '{message_id}' from agent '{agent_name}': {response_obj}")
                        self.pending_acks.pop(message_id, None)
                else:
                    logger.debug(
                        f"Received response for untracked/unknown message ID '{message_id}' from agent '{agent_name}': {response_obj}")
            except IOError as e:
                if stop_event and stop_event.is_set():
                    logger.info(
                        f"IOError on stdout for agent '{agent_name}' during shutdown: {e}")
                else:
                    logger.error(
                        f"IOError reading stdout for agent '{agent_name}': {e}", exc_info=True)
                break
            except ValueError as e:
                logger.error(
                    f"ValueError in ACK listener for agent '{agent_name}', stream likely closed: {e}", exc_info=True)
                break
            except Exception as e:
                logger.error(
                    f"Unexpected error in ACK listener for agent '{agent_name}': {e}", exc_info=True)
                time.sleep(1)
        logger.info(f"ACK listener thread for agent '{agent_name}' stopped.")

    def start_agent(self, agent_name: str, command: List[str]) -> Optional[ProcessDetails]:
        existing_details = self.process_registry.get_process(agent_name)
        if existing_details and existing_details.get('status') == "running":
            pid_val_existing = existing_details.get('pid', 'N/A')
            logger.warning(
                f"Agent '{agent_name}' (PID: {pid_val_existing}) is already registered and running. Cannot start another instance.")
            return existing_details

        process: Optional[subprocess.Popen[str]] = None
        try:
            logger.info(
                f"Attempting to start agent '{agent_name}' with command: {' '.join(command)}")
            process = subprocess.Popen(
                command, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, bufsize=1, errors='replace'
            )
            startup_deadline = time.time() + AGENT_STARTUP_TIMEOUT_SECONDS
            while time.time() < startup_deadline:
                if process.poll() is not None:  # Check if process is not None before poll
                    stderr_output_str = ""
                    if process.stderr:
                        try:
                            stderr_data: List[str] = []
                            for line_err in process.stderr:
                                if not line_err:
                                    break
                                stderr_data.append(line_err)
                            stderr_output_str = "".join(stderr_data)
                        except (IOError, ValueError) as e_read_stderr:
                            logger.warning(
                                f"Could not read stderr from crashed agent '{agent_name}': {e_read_stderr}")

                    pid_val = process.pid  # process is not None here
                    rc_val = process.returncode  # process is not None here
                    logger.error(
                        f"Agent '{agent_name}' (PID: {pid_val}) terminated prematurely during startup "
                        f"with exit code {rc_val}. Stderr: {stderr_output_str.strip()}")
                    self._cleanup_failed_process(process)
                    self.process_registry.update_process_status(
                        agent_name, "failed_to_start")
                    return None
                time.sleep(0.1)

            if process.poll() is None:  # process is not None and running
                pid_val = process.pid
                logger.info(
                    f"Agent '{agent_name}' (PID: {pid_val}) process started and appears ready. Proceeding with registration.")
            else:  # process is not None but terminated
                pid_val = process.pid
                rc_val = process.returncode
                logger.error(
                    f"Agent '{agent_name}' (PID: {pid_val}) unexpectedly terminated after startup check loop with exit code {rc_val}.")
                self._cleanup_failed_process(process)
                self.process_registry.update_process_status(
                    agent_name, "failed_to_start")
                return None

            success = self.process_registry.register_process(
                agent_name, process, command)

            if success:  # process is guaranteed to be non-None here
                pid_val = process.pid
                logger.info(
                    f"Agent '{agent_name}' (PID: {pid_val}) registered successfully.")
                self.process_registry.update_process_status(
                    agent_name, "running")
                if agent_name in self.agent_restart_stats:
                    logger.info(
                        f"Resetting restart stats for successfully started agent '{agent_name}'.")
                    del self.agent_restart_stats[agent_name]
                if process.stdout:
                    stop_event = threading.Event()
                    self._stop_ack_listeners[agent_name] = stop_event
                    ack_thread = threading.Thread(target=self._listen_for_acks, args=(
                        agent_name, process.stdout), daemon=True)
                    ack_thread.start()
                    self.ack_threads[agent_name] = ack_thread
                    logger.info(
                        f"ACK listener thread started for agent '{agent_name}'.")
                else:
                    pid_val = process.pid
                    logger.error(
                        f"Agent '{agent_name}' (PID: {pid_val}) started but stdout is None. Cannot start ACK listener.")
                return self.process_registry.get_process(agent_name)
            else:  # Registration failed, process is not None
                pid_val = process.pid
                logger.error(
                    f"Failed to register agent '{agent_name}' (PID: {pid_val}) even after process started.")
                if process.poll() is None:
                    self._terminate_process_safely(process, agent_name)
                else:
                    self._cleanup_failed_process(process)
                self.process_registry.update_process_status(
                    agent_name, "failed_to_register")
                return None
        except FileNotFoundError as e_fnf:
            logger.error(
                f"Failed to start agent '{agent_name}': Command executable not found ({command[0] if command else 'N/A'}). Error: {e_fnf}", exc_info=True)
            if process:
                self._cleanup_failed_process(process)
            self.process_registry.update_process_status(
                agent_name, "failed_to_start_cmd_not_found")
            return None
        except PermissionError as e_perm:
            logger.error(
                f"Failed to start agent '{agent_name}': Permission denied for command ({command[0] if command else 'N/A'}). Error: {e_perm}", exc_info=True)
            if process:
                self._cleanup_failed_process(process)
            self.process_registry.update_process_status(
                agent_name, "failed_to_start_permission_denied")
            return None
        except OSError as e_os:
            logger.error(
                f"OSError starting agent '{agent_name}': {e_os}", exc_info=True)
            if process:
                self._cleanup_failed_process(process)
            self.process_registry.update_process_status(
                agent_name, "failed_to_start_os_error")
            return None
        except Exception as e:
            logger.error(
                f"Unexpected error starting agent '{agent_name}': {e}", exc_info=True)
            if process:
                if process.poll() is None:
                    self._terminate_process_safely(process, agent_name)
                else:
                    self._cleanup_failed_process(process)
            self.process_registry.update_process_status(
                agent_name, "failed_to_start_exception")
            return None

    def _cleanup_failed_process(self, process: subprocess.Popen[str]):
        if process.stdin and not process.stdin.closed:
            try:
                process.stdin.close()
            except (IOError, ValueError):
                pass
        if process.stdout and not process.stdout.closed:
            try:
                process.stdout.close()
            except (IOError, ValueError):
                pass
        if process.stderr and not process.stderr.closed:
            try:
                process.stderr.close()
            except (IOError, ValueError):
                pass
        try:
            process.wait(timeout=0.1)
        except subprocess.TimeoutExpired:
            pass
        except Exception:
            pass

    def _terminate_process_safely(self, popen_object: Optional[subprocess.Popen[str]], agent_name: str, timeout: int = 5):
        if popen_object is None:
            logger.warning(
                f"Attempted to terminate a None Popen object for agent '{agent_name}'.")
            return

        pid_display = str(
            popen_object.pid) if popen_object.pid is not None else 'N/A'
        logger.info(
            f"Terminating agent process '{agent_name}' (PID: {pid_display}).")
        try:
            if popen_object.stdin and not popen_object.stdin.closed:
                try:
                    popen_object.stdin.close()
                except IOError as e:
                    logger.warning(
                        f"Error closing stdin for agent '{agent_name}' (PID: {pid_display}) during safe terminate: {e}")
                except ValueError:
                    logger.warning(
                        f"ValueError closing stdin for agent '{agent_name}' (PID: {pid_display}), possibly already closed.")

            if popen_object.poll() is None:
                popen_object.terminate()
                try:
                    popen_object.wait(timeout=timeout)
                    logger.info(
                        f"Agent '{agent_name}' (PID: {pid_display}) terminated gracefully.")
                except subprocess.TimeoutExpired:
                    logger.warning(
                        f"Agent '{agent_name}' (PID: {pid_display}) did not terminate gracefully after {timeout}s. Sending SIGKILL.")
                    if popen_object.poll() is None:
                        popen_object.kill()
                        try:
                            popen_object.wait(timeout=timeout)
                            logger.info(
                                f"Agent '{agent_name}' (PID: {pid_display}) killed.")
                        except subprocess.TimeoutExpired:
                            logger.error(
                                f"Agent '{agent_name}' (PID: {pid_display}) did not die even after kill and wait.")
                        except Exception as e_wait_kill:
                            logger.error(
                                f"Error waiting for agent '{agent_name}' (PID: {pid_display}) after kill: {e_wait_kill}")
            else:
                logger.info(
                    f"Agent '{agent_name}' (PID: {pid_display}) was already terminated before _terminate_process_safely active termination step.")
        except ProcessLookupError:
            logger.warning(
                f"Process for agent '{agent_name}' (PID: {pid_display}) not found during termination. Already stopped?")
        except Exception as e:
            logger.error(
                f"Error during _terminate_process_safely for '{agent_name}' (PID: {pid_display}): {e}", exc_info=True)
        finally:
            if popen_object.stdout and not popen_object.stdout.closed:
                try:
                    popen_object.stdout.close()
                except (IOError, ValueError):
                    pass
            if popen_object.stderr and not popen_object.stderr.closed:
                try:
                    popen_object.stderr.close()
                except (IOError, ValueError):
                    pass

    def stop_agent(self, agent_name: str) -> bool:
        if agent_name in self._stop_ack_listeners:
            logger.info(
                f"Signaling ACK listener for agent '{agent_name}' to stop.")
            self._stop_ack_listeners[agent_name].set()
            ack_thread = self.ack_threads.get(agent_name)
            if ack_thread and ack_thread.is_alive():
                logger.debug(
                    f"Waiting for ACK listener thread of '{agent_name}' to join...")
                ack_thread.join(timeout=5)
                if ack_thread.is_alive():
                    logger.warning(
                        f"ACK listener thread for '{agent_name}' did not join in time.")
            self.ack_threads.pop(agent_name, None)
            self._stop_ack_listeners.pop(agent_name, None)
            logger.info(f"ACK listener for '{agent_name}' resources released.")

        process_details = self.process_registry.get_process(agent_name)
        if not process_details:
            logger.warning(f"Agent '{agent_name}' not found. Cannot stop.")
            return False

        popen_object = process_details.get("popen_object")
        # This pid is from registry, could be None
        pid_val = process_details.get("pid")
        pid_display_stop = str(pid_val) if pid_val is not None else 'N/A'

        if not popen_object:
            logger.warning(
                f"Agent '{agent_name}' (PID: {pid_display_stop}) has no Popen object in registry. Attempting to unregister.")
            self.process_registry.unregister_process(agent_name)
            self._clear_pending_acks_for_agent(agent_name)
            return True

        # Use popen_object.pid for logging if popen_object exists, otherwise use pid_from_registry
        pid_for_log_stop = str(popen_object.pid) if hasattr(
            popen_object, 'pid') and popen_object.pid is not None else pid_display_stop

        try:
            if popen_object.poll() is None:
                self._terminate_process_safely(popen_object, agent_name)
            else:
                logger.info(
                    f"Agent '{agent_name}' (PID: {pid_for_log_stop}) already terminated prior to stop_agent call. Cleaning up pipes.")
                self._cleanup_failed_process(popen_object)

            self.process_registry.unregister_process(agent_name)
            logger.info(f"Agent '{agent_name}' stopped and unregistered.")
            self._clear_pending_acks_for_agent(agent_name)
            if agent_name in self.agent_restart_stats:
                del self.agent_restart_stats[agent_name]
            return True
        except Exception as e:
            logger.error(
                f"Error stopping agent '{agent_name}': {e}", exc_info=True)
            self.process_registry.unregister_process(agent_name)
            self._clear_pending_acks_for_agent(agent_name)
            return False

    def stop_ack_monitor(self):
        if self._ack_monitor_thread and self._ack_monitor_thread.is_alive():
            logger.info("Signaling central ACK monitor thread to stop.")
            self._stop_ack_monitor_event.set()
            self._ack_monitor_thread.join(timeout=5)
            if self._ack_monitor_thread.is_alive():
                logger.warning(
                    "Central ACK monitor thread did not stop in time.")
            else:
                logger.info("Central ACK monitor thread stopped successfully.")
            self._ack_monitor_thread = None

    def _clear_pending_acks_for_agent(self, agent_name: str):
        ids_to_remove = [msg_id for msg_id, details in self.pending_acks.items(
        ) if details.get('agent_name') == agent_name]
        if ids_to_remove:
            logger.info(
                f"Clearing {len(ids_to_remove)} pending ACK(s) for stopped agent '{agent_name}'.")
            for msg_id in ids_to_remove:
                self.pending_acks.pop(msg_id, None)

    def check_agent_status(self, agent_name: str) -> Optional[str]:
        process_details = self.process_registry.get_process(agent_name)
        if not process_details:
            logger.debug(f"Agent '{agent_name}' not found in registry.")
            return None
        popen_object = process_details.get("popen_object")
        current_registry_status = process_details.get("status")
        if not popen_object:
            logger.warning(
                f"Agent '{agent_name}' has no Popen object in registry. Status: {current_registry_status}")
            return current_registry_status
        if popen_object.poll() is None:
            if current_registry_status != "running":
                self.process_registry.update_process_status(
                    agent_name, "running")
            return "running"
        else:
            if current_registry_status != "terminated" and not current_registry_status.startswith("failed_"):
                self.process_registry.update_process_status(
                    agent_name, "terminated")
            return "terminated"

    def monitor_and_restart_agents(self, check_interval_seconds: int = AGENT_MONITOR_INTERVAL_SECONDS):
        logger.info(
            f"Starting agent monitoring loop (interval: {check_interval_seconds}s)...")
        while True:
            time.sleep(check_interval_seconds)
            all_process_details_list = self.process_registry.list_processes()
            if not all_process_details_list:
                logger.debug("No processes registered to monitor.")
                continue
            logger.debug(
                f"Monitoring {len(all_process_details_list)} processes...")
            for process_details in all_process_details_list:
                agent_name = process_details['name']
                pid_from_registry = process_details.get('pid')
                command_list = process_details['command']
                current_status_in_registry = process_details['status']
                popen_object = process_details.get('popen_object')

                pid_display = str(
                    pid_from_registry) if pid_from_registry is not None else 'N/A'

                if not popen_object:
                    logger.warning(
                        f"Agent '{agent_name}' (PID: {pid_display}) is missing Popen object in registry. Cannot reliably monitor or restart.")
                    if current_status_in_registry not in ["terminated", "failed_to_start", "failed_to_register", "unknown_missing_popen"] and not current_status_in_registry.startswith("failed_"):
                        self.process_registry.update_process_status(
                            agent_name, "unknown_missing_popen")
                    continue

                process_exit_code = popen_object.poll()
                # From live Popen object
                actual_pid_display = str(
                    popen_object.pid) if popen_object.pid is not None else 'N/A'

                if process_exit_code is None:
                    if current_status_in_registry != "running":
                        logger.info(
                            f"Agent '{agent_name}' (PID: {actual_pid_display}) is running but status was '{current_status_in_registry}'. Updating to 'running'.")
                        self.process_registry.update_process_status(
                            agent_name, "running")
                    continue

                logger.warning(
                    f"Agent '{agent_name}' (PID: {actual_pid_display}) found terminated with exit code {process_exit_code}. Registered status was '{current_status_in_registry}'.")

                new_status_before_restart_check = "terminated_unexpectedly"
                if not current_status_in_registry.startswith("failed_") and current_status_in_registry != new_status_before_restart_check:
                    self.process_registry.update_process_status(
                        agent_name, new_status_before_restart_check)

                # Re-fetch status after potential update
                refetched_details = self.process_registry.get_process(
                    agent_name)
                current_status_in_registry = refetched_details.get(
                    'status', 'unknown') if refetched_details else 'unknown'

                if current_status_in_registry == "terminated_unexpectedly" or \
                   current_status_in_registry.startswith("failed_to_start") or \
                   current_status_in_registry == "running":

                    if not command_list:
                        logger.error(
                            f"Cannot restart agent '{agent_name}' (PID: {actual_pid_display}): command not found in registry.")
                        continue

                    now = time.time()
                    stats: Dict[str, Union[int, float]] = self.agent_restart_stats.get(
                        agent_name, {'attempts': 0, 'first_attempt_timestamp': 0.0})

                    if stats['attempts'] >= AGENT_MAX_RESTART_ATTEMPTS and \
                       (now - float(stats['first_attempt_timestamp'])) < AGENT_RESTART_COUNT_RESET_PERIOD_SECONDS:  # Ensure float for comparison
                        logger.error(
                            f"Agent '{agent_name}' (PID: {actual_pid_display}) has reached max restart attempts ({stats['attempts']}/{AGENT_MAX_RESTART_ATTEMPTS}). Not attempting further restarts.")
                        if current_status_in_registry != "failed_too_many_restarts":
                            self.process_registry.update_process_status(
                                agent_name, "failed_too_many_restarts")
                        continue

                    if (now - float(stats['first_attempt_timestamp'])) >= AGENT_RESTART_COUNT_RESET_PERIOD_SECONDS:
                        logger.info(
                            f"Resetting restart attempt count for agent '{agent_name}' (PID: {actual_pid_display}).")
                        stats = {'attempts': 0, 'first_attempt_timestamp': 0.0}

                    if stats['attempts'] == 0:
                        stats['first_attempt_timestamp'] = now
                    # Ensure attempts is int
                    stats['attempts'] = int(stats['attempts']) + 1
                    self.agent_restart_stats[agent_name] = stats

                    logger.info(
                        f"Attempting to restart agent '{agent_name}' (PID: {actual_pid_display}) (Attempt {stats['attempts']}/{AGENT_MAX_RESTART_ATTEMPTS})")

                    if agent_name in self._stop_ack_listeners:
                        self._stop_ack_listeners[agent_name].set()
                        ack_thread = self.ack_threads.get(agent_name)
                        if ack_thread and ack_thread.is_alive():
                            ack_thread.join(timeout=1)
                        self.ack_threads.pop(agent_name, None)
                        self._stop_ack_listeners.pop(agent_name, None)

                    self._clear_pending_acks_for_agent(agent_name)
                    self.process_registry.unregister_process(agent_name)

                    restarted_info = self.start_agent(agent_name, command_list)
                    if restarted_info and restarted_info.get('status') == "running":
                        restarted_pid = restarted_info.get('pid', 'N/A')
                        logger.info(
                            f"Agent '{agent_name}' restarted successfully with new PID {restarted_pid}. Attempt {stats['attempts']} successful.")
                    else:
                        logger.error(
                            f"Failed to restart agent '{agent_name}' (Original PID: {actual_pid_display}). Attempt {stats['attempts']}/{AGENT_MAX_RESTART_ATTEMPTS}.")
                        if stats['attempts'] >= AGENT_MAX_RESTART_ATTEMPTS:
                            final_check_details_after_fail = self.process_registry.get_process(
                                agent_name)
                            final_status_after_fail = final_check_details_after_fail.get(
                                'status') if final_check_details_after_fail else "unknown_after_final_restart_fail"
                            if final_status_after_fail != "failed_too_many_restarts":
                                self.process_registry.update_process_status(
                                    agent_name, "failed_too_many_restarts")

                elif current_status_in_registry not in [
                        "terminated", "terminated_unexpectedly", "failed_too_many_restarts",
                        "failed_to_start", "failed_to_register", "failed_to_start_cmd_not_found",
                        "failed_to_start_permission_denied", "failed_to_start_os_error",
                        "failed_to_start_exception", "unknown_missing_popen"
                ] and not current_status_in_registry.startswith("failed_restart_attempt_"):
                    logger.info(
                        f"Agent '{agent_name}' (PID: {actual_pid_display}) found terminated. Status was '{current_status_in_registry}'. Updating to 'terminated'.")
                    self.process_registry.update_process_status(
                        agent_name, "terminated")

    def send_jsonrpc_message(self, agent_name: str, method: str, params: Optional[Dict[str, Any]] = None, message_id: Optional[str] = None, is_retry: bool = False) -> Optional[Dict[str, Any]]:
        process_details = self.process_registry.get_process(agent_name)
        if not process_details:
            logger.error(
                f"Cannot send message to agent '{agent_name}': Not found in registry.")
            return None

        popen_object = process_details.get("popen_object")
        pid_from_registry = process_details.get("pid")
        current_status = process_details.get("status")
        pid_display = str(
            pid_from_registry) if pid_from_registry is not None else 'N/A'

        if not popen_object:
            logger.error(
                f"Cannot send message to agent '{agent_name}' (PID: {pid_display}): Popen object missing from registry.")
            return None

        # From live Popen object
        actual_pid_display = str(
            popen_object.pid) if popen_object.pid is not None else 'N/A'

        if popen_object.poll() is not None:
            rc_val = popen_object.returncode if hasattr(
                popen_object, 'returncode') else 'N/A'
            logger.error(
                f"Cannot send message to agent '{agent_name}' (PID: {actual_pid_display}): Process is not running (polled as terminated). "
                f"Registered status was '{current_status}'. Exit code: {rc_val}.")
            if current_status != "terminated" and not current_status.startswith("failed_"):
                self.process_registry.update_process_status(
                    agent_name, "terminated")
            return None

        if popen_object.stdin is None or popen_object.stdin.closed:
            logger.error(
                f"Cannot send message to agent '{agent_name}' (PID: {actual_pid_display}): Process stdin is None or closed.")
            return None

        if current_status != "running":
            if popen_object.poll() is None:
                logger.warning(
                    f"Agent '{agent_name}' (PID: {actual_pid_display}) is polled as running, but registered status is '{current_status}'. "
                    f"Proceeding with send and updating status to 'running'.")
                self.process_registry.update_process_status(
                    agent_name, "running")
            else:
                rc_val = popen_object.returncode if hasattr(
                    popen_object, 'returncode') else 'N/A'
                logger.error(
                    f"Cannot send message to agent '{agent_name}' (PID: {actual_pid_display}): Process not running (poll confirmed). Status '{current_status}', exit: {rc_val}.")
                return None

        final_message_id: Optional[str] = None
        try:
            actual_message_id_for_request = message_id
            request_obj = create_jsonrpc_request(
                method=method, params=params, message_id=actual_message_id_for_request)
            final_message_id = request_obj['id']

            serialized_message = serialize_jsonrpc_message(request_obj)
            message_to_send = serialized_message + "\n"

            logger.info(
                f"Sending JSON-RPC to '{agent_name}' (PID: {actual_pid_display}, ID: {final_message_id}): Method='{method}'{' (RETRY)' if is_retry else ''}")

            try:
                if popen_object.stdin:
                    popen_object.stdin.write(message_to_send)
                    popen_object.stdin.flush()
                else:
                    logger.error(
                        f"Critical: stdin became None just before write for agent '{agent_name}' (PID: {actual_pid_display}).")
                    return None
            except (BrokenPipeError, OSError, ValueError) as e_pipe:
                logger.error(
                    f"Pipe error sending message to agent '{agent_name}' (PID: {actual_pid_display}, ID: {final_message_id}): {e_pipe}", exc_info=True)
                if final_message_id and not is_retry:
                    self.pending_acks.pop(final_message_id, None)
                return None

            if not is_retry and final_message_id is not None:
                self.pending_acks[final_message_id] = {
                    'agent_name': agent_name, 'timestamp': time.time(), 'method': method,
                    'params': params, 'message_content': request_obj, 'retry_count': 0
                }
                logger.debug(
                    f"Message ID '{final_message_id}' added to pending_acks for '{agent_name}'.")

            logger.info(
                f"Waiting for ACK for message ID '{final_message_id}' from '{agent_name}' (sync timeout: {ACK_SYNC_WAIT_TIMEOUT_SECONDS}s).")
            ack_wait_start_time = time.time()
            agent_stdout = popen_object.stdout

            if not agent_stdout:
                logger.error(
                    f"Stdout not available for agent '{agent_name}' to wait for ACK for message ID '{final_message_id}'.")
                return None

            try:
                while (time.time() - ack_wait_start_time) < ACK_SYNC_WAIT_TIMEOUT_SECONDS:
                    raw_line_from_stdout = None
                    try:
                        raw_line_from_stdout = agent_stdout.readline()
                    except ValueError as ve:
                        logger.error(
                            f"ValueError reading stdout for ACK from agent '{agent_name}' (ID: {final_message_id}): {ve}")
                        return None
                    except IOError as ioe:
                        logger.error(
                            f"IOError reading stdout for ACK from agent '{agent_name}' (ID: {final_message_id}): {ioe}")
                        return None
                    if not raw_line_from_stdout:
                        logger.warning(
                            f"EOF received from agent '{agent_name}' stdout while waiting for ACK for '{final_message_id}'.")
                        return None
                    line_content = raw_line_from_stdout.strip()
                    if not line_content:
                        if (time.time() - ack_wait_start_time) >= ACK_SYNC_WAIT_TIMEOUT_SECONDS:
                            break
                        continue
                    logger.debug(
                        f"Agent '{agent_name}' (ID: {final_message_id}) ACK wait, stdout: {line_content}")
                    try:
                        response_obj_ack = deserialize_jsonrpc_message(
                            line_content)
                        if response_obj_ack.get("id") == final_message_id:
                            if "result" in response_obj_ack or "error" in response_obj_ack:
                                logger.info(
                                    f"Received synchronous ACK for message ID '{final_message_id}' from agent '{agent_name}': {response_obj_ack}")
                                if final_message_id:
                                    self.pending_acks.pop(
                                        final_message_id, None)
                                return response_obj_ack
                            else:
                                logger.warning(
                                    f"Received JSON-RPC for ID '{final_message_id}' from '{agent_name}' but no result/error: {response_obj_ack}")
                                if final_message_id:
                                    self.pending_acks.pop(
                                        final_message_id, None)
                                return response_obj_ack
                        else:
                            logger.debug(
                                f"Received msg from '{agent_name}' for another ID '{response_obj_ack.get('id')}' or a notification, while waiting for '{final_message_id}'. Content: {line_content}")
                    except json.JSONDecodeError:
                        logger.warning(
                            f"Non-JSON line from agent '{agent_name}' while waiting for ACK for '{final_message_id}': {line_content}")
                    if (time.time() - ack_wait_start_time) >= ACK_SYNC_WAIT_TIMEOUT_SECONDS:
                        break
                logger.warning(
                    f"Timeout ({ACK_SYNC_WAIT_TIMEOUT_SECONDS}s) waiting for synchronous ACK for message ID '{final_message_id}' from agent '{agent_name}'.")
                return None
            except Exception as e_ack_wait:
                logger.error(
                    f"Unexpected error while waiting for synchronous ACK from agent '{agent_name}' (ID: {final_message_id}): {e_ack_wait}", exc_info=True)
                return None
        except IOError as e_send:
            logger.error(
                f"IOError sending message to agent '{agent_name}' (PID: {pid_display}): {e_send}", exc_info=True)
            if final_message_id:
                self.pending_acks.pop(final_message_id, None)
            return None
        except Exception as e_send_unexpected:
            logger.error(
                f"Unexpected error sending message to agent '{agent_name}' (PID: {pid_display}): {e_send_unexpected}", exc_info=True)
            if final_message_id:
                self.pending_acks.pop(final_message_id, None)
            return None
