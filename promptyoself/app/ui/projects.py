# -*- coding: utf-8 -*-
"""Project UI views."""
from flask import Blueprint, render_template, request, flash, redirect, url_for, abort

from app.forms import ProjectForm
from app.models import Project
from app.extensions import db
from app.utils import flash_errors

blueprint = Blueprint("projects", __name__, url_prefix="/projects")


@blueprint.route("/")
def list_projects():
    """List all projects with pagination and filtering."""
    page = request.args.get("page", 1, type=int)
    # Default to 10 items per page
    per_page = request.args.get("per_page", 10, type=int)
    name_filter = request.args.get("name", None, type=str)

    query = Project.query
    if name_filter:
        query = query.filter(Project.name.ilike(f"%{name_filter}%"))

    paginated_projects = query.order_by(Project.name).paginate(
        page=page, per_page=per_page, error_out=False)

    return render_template("projects/list.html", paginated_projects=paginated_projects, name_filter=name_filter)


@blueprint.route("/<int:project_id>")
def view_project(project_id):
    """View a specific project with its details."""
    project = Project.query.get_or_404(project_id)
    return render_template("projects/detail.html", project=project)


@blueprint.route("/new", methods=["GET", "POST"])
def new_project():
    """Create a new project."""
    form = ProjectForm(request.form)
    if form.validate_on_submit():
        project = Project.create(
            name=form.name.data,
            description=form.description.data,
        )
        flash(f"Project '{project.name}' created successfully.", "success")
        return redirect(url_for("projects.list_projects"))
    else:
        flash_errors(form)
    return render_template("projects/form.html", form=form, title="New Project")


@blueprint.route("/<int:project_id>/edit", methods=["GET", "POST"])
def edit_project(project_id):
    """Edit a project."""
    project = Project.query.get_or_404(project_id)
    form = ProjectForm(request.form, obj=project)

    if form.validate_on_submit():
        form.populate_obj(project)
        db.session.commit()
        flash(f"Project '{project.name}' updated successfully.", "success")
        return redirect(url_for("projects.list_projects"))
    else:
        flash_errors(form)

    return render_template("projects/form.html", form=form, title="Edit Project", project=project)


@blueprint.route("/<int:project_id>/delete", methods=["POST"])
def delete_project(project_id):
    """Delete a project."""
    project = Project.query.get_or_404(project_id)
    name = project.name
    db.session.delete(project)
    db.session.commit()
    flash(f"Project '{name}' deleted successfully.", "info")
    return redirect(url_for("projects.list_projects"))
