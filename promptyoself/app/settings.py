# -*- coding: utf-8 -*-
"""Application configuration.

Most configuration is set via environment variables.

For local development, use a .env file to set
environment variables.
"""
from environs import Env

env = Env()
env.read_env()

ENV = env.str("FLASK_ENV", default="production")
DEBUG = ENV == "development"
SQLALCHEMY_DATABASE_URI = env.str("DATABASE_URL")
SECRET_KEY = env.str("SECRET_KEY")
SEND_FILE_MAX_AGE_DEFAULT = env.int("SEND_FILE_MAX_AGE_DEFAULT")
# Internal API authentication
INTERNAL_AGENT_API_KEY = env.str("INTERNAL_AGENT_API_KEY", default=None)
BCRYPT_LOG_ROUNDS = env.int("BCRYPT_LOG_ROUNDS", default=13)
DEBUG_TB_ENABLED = DEBUG
DEBUG_TB_INTERCEPT_REDIRECTS = False
CACHE_TYPE = (
    # Can be "MemcachedCache", "RedisCache", etc.
    "flask_caching.backends.SimpleCache"
)
SQLALCHEMY_TRACK_MODIFICATIONS = False

# APScheduler Configuration
SCHEDULER_JOBSTORES = {
    "default": {"type": "sqlalchemy", "url": SQLALCHEMY_DATABASE_URI}
}
SCHEDULER_EXECUTORS = {"default": {"type": "threadpool", "max_workers": 20}}
SCHEDULER_JOB_DEFAULTS = {"coalesce": False, "max_instances": 3}
SCHEDULER_API_ENABLED = True
SCHEDULER_TIMEZONE = "UTC"
# Logging configuration
LOG_LEVEL = env.str("LOG_LEVEL", default="INFO" if ENV ==
                    "production" else "DEBUG")
LOG_TO_STDOUT = env.bool("LOG_TO_STDOUT", default=True)

# Security settings for production
if ENV == "production":
    # Force HTTPS in production
    PREFERRED_URL_SCHEME = "https"
    # Session cookie settings
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = "Lax"
    # CSRF settings
    WTF_CSRF_TIME_LIMIT = None  # No time limit for CSRF tokens
