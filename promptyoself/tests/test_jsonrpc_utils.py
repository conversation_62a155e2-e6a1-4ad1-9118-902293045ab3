import json
import pytest

from app.agents import jsonrpc_utils as utils


def test_create_jsonrpc_request_with_id():
    req = utils.create_jsonrpc_request("ping", {"x": 1}, message_id="123")
    assert req["jsonrpc"] == "2.0"
    assert req["method"] == "ping"
    assert req["params"] == {"x": 1}
    assert req["id"] == "123"


def test_create_jsonrpc_request_without_id_generates_uuid():
    req1 = utils.create_jsonrpc_request("ping")
    req2 = utils.create_jsonrpc_request("ping")
    assert req1["id"] != req2["id"]


def test_serialize_deserialize_roundtrip():
    req = utils.create_jsonrpc_request("echo", {"msg": "hi"}, "42")
    s = utils.serialize_jsonrpc_message(req)
    assert isinstance(s, str)
    loaded = utils.deserialize_jsonrpc_message(s)
    assert loaded == req


@pytest.mark.parametrize("bad_obj", [{"set": {1}}, {"bytes": b"a"}])
def test_serialize_invalid_object_raises(bad_obj):
    with pytest.raises(TypeError):
        utils.serialize_jsonrpc_message(bad_obj)


def test_deserialize_invalid_json_raises():
    with pytest.raises(json.JSONDecodeError):
        utils.deserialize_jsonrpc_message("{bad json}")
