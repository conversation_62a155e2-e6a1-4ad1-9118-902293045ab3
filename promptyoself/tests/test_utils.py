from types import SimpleNamespace

from app.utils import flash_errors


class DummyField:
    def __init__(self, label):
        self.label = SimpleNamespace(text=label)


class DummyForm:
    def __init__(self):
        self.errors = {"name": ["required"], "email": ["invalid"]}
        self.name = Dummy<PERSON>ield("Name")
        self.email = Dummy<PERSON>ield("Email")


class Recorder:
    def __init__(self):
        self.messages = []

    def flash(self, msg, category):
        self.messages.append((msg, category))


def test_flash_errors(monkeypatch):
    form = DummyForm()
    rec = Recorder()
    monkeypatch.setattr("app.utils.flash", rec.flash)
    flash_errors(form, category="err")
    assert (
        "Name - required",
        "err",
    ) in rec.messages and ("Email - invalid", "err") in rec.messages
