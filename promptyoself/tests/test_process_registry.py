import types
from app.agents.process_registry import ProcessRegistry


class DummyPopen:
    def __init__(self, pid=123):
        self.pid = pid
        self._terminated = False

    def poll(self):
        return None if not self._terminated else 0


def test_register_and_retrieve():
    registry = ProcessRegistry()
    proc = DummyPopen()
    assert registry.register_process("agent", proc, ["agent"])
    info = registry.get_process("agent")
    assert info["pid"] == proc.pid
    assert registry.is_registered("agent")


def test_register_duplicate_fails():
    registry = ProcessRegistry()
    proc = DummyPopen()
    registry.register_process("agent", proc, ["agent"])
    assert not registry.register_process("agent", proc, ["agent"])


def test_update_status_and_list_unregistered():
    registry = ProcessRegistry()
    proc = DummyPopen()
    registry.register_process("agent1", proc, ["agent"])
    registry.update_process_status("agent1", "stopped")
    listed = registry.list_processes()
    assert listed[0]["status"] == "stopped"
    assert registry.unregister_process("agent1")
    assert not registry.is_registered("agent1")

def test_missing_process_methods_return_expected():
    registry = ProcessRegistry()
    assert registry.get_process("none") is None
    assert not registry.unregister_process("none")
    assert registry.list_processes() == []
    assert not registry.update_process_status("none", "x")

def test_list_processes_updates_terminated_status():
    class TerminatingPopen(DummyPopen):
        def poll(self):
            return 1
    registry = ProcessRegistry()
    proc = TerminatingPopen(pid=2)
    registry.register_process("agent", proc, ["agent"])
    processes = registry.list_processes()
    assert processes[0]["status"] == "terminated"
