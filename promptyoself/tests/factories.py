# -*- coding: utf-8 -*-
"""Factories to help in tests."""
from factory import Sequence, SubFactory, LazyAttribute
from factory.alchemy import SQLAlchemyModelFactory
import datetime as dt

from app.database import db
from app.models import User, Project, Task, Reminder


class BaseFactory(SQLAlchemyModelFactory):
    """Base factory."""

    class Meta:
        """Factory configuration."""

        abstract = True
        sqlalchemy_session = db.session


class UserFactory(BaseFactory):
    """User factory."""

    username = Sequence(lambda n: f"user{n}")
    email = Sequence(lambda n: f"user{n}@example.com")
    active = True

    class Meta:
        """Factory configuration."""

        model = User


class ProjectFactory(BaseFactory):
    """Project factory."""

    name = Sequence(lambda n: f"project{n}")
    description = Sequence(lambda n: f"Project {n} description")

    class Meta:
        model = Project


class TaskFactory(BaseFactory):
    """Task factory."""

    name = Sequence(lambda n: f"task{n}")
    description = Sequence(lambda n: f"Task {n} description")
    project = SubFactory(ProjectFactory)

    class Meta:
        model = Task


class ReminderFactory(BaseFactory):
    """Reminder factory."""

    message = Sequence(lambda n: f"Reminder {n}")
    next_run = Sequence(lambda n: dt.datetime.now(dt.timezone.utc) + dt.timedelta(days=n))
    process_name = "echo_agent.py"
    task = SubFactory(TaskFactory)
    task_id = LazyAttribute(lambda o: o.task.id)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        kwargs.pop("task", None)
        return super()._create(model_class, *args, **kwargs)

    class Meta:
        model = Reminder
