import datetime as dt
import pytest

from app.jobs.reminder_jobs import calculate_next_run


@pytest.mark.parametrize(
    "unit,value,expected",
    [
        ("minutes", 5, dt.timedelta(minutes=5)),
        ("hours", 2, dt.timedelta(hours=2)),
        ("days", 1, dt.timedelta(days=1)),
        ("weeks", 1, dt.timedelta(weeks=1)),
    ],
)
def test_calculate_next_run_valid(unit, value, expected):
    base = dt.datetime(2024, 1, 1, tzinfo=dt.timezone.utc)
    result = calculate_next_run(base, unit, value)
    assert result == base + expected


def test_calculate_next_run_invalid_unit():
    base = dt.datetime.now(dt.timezone.utc)
    assert calculate_next_run(base, "invalid", 1) is None


def test_calculate_next_run_invalid_value():
    base = dt.datetime.now(dt.timezone.utc)
    assert calculate_next_run(base, "minutes", 0) is None
