"""Tests for template context processors and filters."""
from datetime import datetime


def test_current_year_injection(app):
    """Test that current_year is correctly injected into template context."""
    with app.app_context():
        # Get the template context processors
        context_processors = app.template_context_processors[None]
        
        # Find the inject_current_year processor
        inject_current_year = None
        for processor in context_processors:
            if processor.__name__ == 'inject_current_year':
                inject_current_year = processor
                break
        
        assert inject_current_year is not None, "inject_current_year context processor not found"
        
        # Test the processor returns current year
        context = inject_current_year()
        assert 'current_year' in context
        assert context['current_year'] == datetime.now().year


def test_date_filter(app):
    """Test that the date filter is correctly registered and works."""
    with app.app_context():
        # Get the Jinja environment
        jinja_env = app.jinja_env
        
        # Check that the date filter is registered
        assert 'date' in jinja_env.filters
        
        date_filter = jinja_env.filters['date']
        
        # Test with datetime object
        test_date = datetime(2023, 12, 25, 15, 30, 45)
        formatted = date_filter(test_date, '%Y-%m-%d')
        assert formatted == '2023-12-25'
        
        # Test with "now" string
        now_formatted = date_filter("now", '%Y')
        current_year = str(datetime.utcnow().year)
        assert now_formatted == current_year
        
        # Test with default format
        default_formatted = date_filter(test_date)
        assert default_formatted == '2023-12-25 15:30:45'
