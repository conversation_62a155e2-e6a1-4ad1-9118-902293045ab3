# -*- coding: utf-8 -*-
"""Tests for the date template filter."""

import pytest
import datetime as dt
from app import create_app


@pytest.fixture
def app():
    """Create application for the tests."""
    app = create_app("tests.settings")
    return app


@pytest.fixture
def date_filter(app):
    """Get the date filter from the app."""
    with app.app_context():
        return app.jinja_env.filters['date']


def test_date_filter_with_datetime_object(date_filter):
    """Test date filter with datetime object."""
    test_datetime = dt.datetime(2023, 12, 25, 15, 30, 45)
    result = date_filter(test_datetime)
    assert result == "2023-12-25 15:30:45"


def test_date_filter_with_date_object(date_filter):
    """Test date filter with date object."""
    test_date = dt.date(2023, 12, 25)
    result = date_filter(test_date)
    assert result == "2023-12-25 00:00:00"


def test_date_filter_with_custom_format(date_filter):
    """Test date filter with custom format string."""
    test_datetime = dt.datetime(2023, 12, 25, 15, 30, 45)
    result = date_filter(test_datetime, "%Y-%m-%d")
    assert result == "2023-12-25"


def test_date_filter_with_now_string(date_filter):
    """Test date filter with 'now' string."""
    result = date_filter("now")
    # Should return current UTC time in default format
    # We can't test exact time, but we can test format
    assert len(result) == 19  # "YYYY-MM-DD HH:MM:SS" format
    assert result[4] == "-"
    assert result[7] == "-"
    assert result[10] == " "
    assert result[13] == ":"
    assert result[16] == ":"


def test_date_filter_with_now_string_custom_format(date_filter):
    """Test date filter with 'now' string and custom format."""
    result = date_filter("now", "%Y-%m-%d")
    # Should return current UTC date in YYYY-MM-DD format
    assert len(result) == 10  # "YYYY-MM-DD" format
    assert result[4] == "-"
    assert result[7] == "-"


def test_date_filter_with_non_date_value(date_filter):
    """Test date filter with non-date value returns unchanged."""
    test_string = "not a date"
    result = date_filter(test_string)
    assert result == "not a date"


def test_date_filter_with_number(date_filter):
    """Test date filter with number returns unchanged."""
    test_number = 12345
    result = date_filter(test_number)
    assert result == 12345


def test_date_filter_with_none(date_filter):
    """Test date filter with None returns unchanged."""
    result = date_filter(None)
    assert result is None


def test_date_filter_with_empty_string(date_filter):
    """Test date filter with empty string returns unchanged."""
    result = date_filter("")
    assert result == ""


def test_date_filter_with_timezone_aware_datetime(date_filter):
    """Test date filter with timezone-aware datetime."""
    test_datetime = dt.datetime(
        2023, 12, 25, 15, 30, 45, tzinfo=dt.timezone.utc)
    result = date_filter(test_datetime)
    assert result == "2023-12-25 15:30:45"


def test_date_filter_with_microseconds(date_filter):
    """Test date filter with datetime containing microseconds."""
    test_datetime = dt.datetime(2023, 12, 25, 15, 30, 45, 123456)
    result = date_filter(test_datetime)
    assert result == "2023-12-25 15:30:45"


def test_date_filter_with_complex_format(date_filter):
    """Test date filter with complex format string."""
    test_datetime = dt.datetime(2023, 12, 25, 15, 30, 45)
    result = date_filter(test_datetime, "%A, %B %d, %Y at %I:%M %p")
    assert result == "Monday, December 25, 2023 at 03:30 PM"


def test_date_filter_edge_cases(date_filter):
    """Test date filter with edge case values."""
    # Test with minimum datetime
    min_datetime = dt.datetime(1, 1, 1)
    result = date_filter(min_datetime)
    # Python's strftime doesn't zero-pad years < 1000
    assert result == "1-01-01 00:00:00"

    # Test with maximum year (within reasonable bounds)
    max_datetime = dt.datetime(9999, 12, 31, 23, 59, 59)
    result = date_filter(max_datetime)
    assert result == "9999-12-31 23:59:59"


def test_date_filter_with_special_format(date_filter):
    """Test date filter with special format string."""
    test_datetime = dt.datetime(2023, 12, 25, 15, 30, 45)
    # Test with a format that includes weekday
    result = date_filter(test_datetime, "%A")
    assert result == "Monday"
