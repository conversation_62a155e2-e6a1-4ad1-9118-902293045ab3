# Environment variable overrides for local development
FLASK_APP=autoapp.py
FLASK_DEBUG=1
FLASK_ENV=development
DATABASE_URL=sqlite:///instance/dev.sqlite3
SECRET_KEY=not-so-secret-dev-key
# In production, set to a higher number, like 31556926
SEND_FILE_MAX_AGE_DEFAULT=0

# Optional: PostgreSQL configuration for production
# DATABASE_URL=postgresql://promptyoself:password@localhost:5432/promptyoself

# Optional: APScheduler configuration
# SCHEDULER_API_ENABLED=true

# API Key for internal agent authentication
# INTERNAL_AGENT_API_KEY=your-secure-random-api-key-here
