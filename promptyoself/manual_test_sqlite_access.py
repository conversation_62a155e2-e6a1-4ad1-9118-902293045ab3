import os
import sys
import sqlite3

db_path = "/workspace/promptyoself/instance/test_direct.db"
instance_dir = os.path.dirname(db_path)

if __name__ == "__main__":
    print(f"Attempting to use database path: {db_path}")
    print(f"Instance directory: {instance_dir}")

    try:
        # Ensure directory exists
        print(f"Checking if instance directory '{instance_dir}' exists...")
        if not os.path.exists(instance_dir):
            print(f"Instance directory does not exist. Creating it...")
            os.makedirs(instance_dir, exist_ok=True)
            print("Instance directory created.")
        else:
            print("Instance directory already exists.")

        print(f"Attempting to connect to SQLite database at: {db_path}")
        conn = sqlite3.connect(db_path)
        print(f"Successfully connected to/created {db_path}")

        conn.execute(
            "CREATE TABLE IF NOT EXISTS direct_test_table (id INTEGER PRIMARY KEY, name TEXT)"
        )
        print("Successfully executed CREATE TABLE IF NOT EXISTS")

        conn.execute(
            "INSERT INTO direct_test_table (name) VALUES (?)", ("Direct SQLite Test",))
        print("Successfully executed INSERT")
        conn.commit()
        print("Successfully committed changes")

        cursor = conn.cursor()
        cursor.execute("SELECT * FROM direct_test_table")
        data = cursor.fetchall()
        print(f"Data from direct_test_table: {data}")

        conn.close()
        print("Successfully closed connection")

        # Clean up the test database file
        if os.path.exists(db_path):
            os.remove(db_path)
            print(f"Successfully removed test database file: {db_path}")

        print("SQLite direct access test successful.")
        sys.exit(0)

    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        print(f"Error class: {type(e)}")
        print(f"Error args: {e.args}")
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        print(f"Error class: {type(e)}")
        print(f"Error args: {e.args}")
        sys.exit(1)
