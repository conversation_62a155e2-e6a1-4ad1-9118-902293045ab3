# Everything needed in production

# Flask
click>=7.0
Flask==3.1.1
Werkzeug==3.1.3

# Database
Flask-SQLAlchemy==3.1.1
psycopg2-binary==2.9.10
SQLAlchemy==2.0.41

# Migrations
Flask-Migrate==4.1.0

# Forms
email-validator==2.2.0
Flask-WTF==1.2.2
WTForms==3.2.1

# Concurrency
gevent==24.2.1
gunicorn>=19.9.0
supervisor==4.2.5

# Flask Static Digest
Flask-Static-Digest==0.4.1

# Auth
Flask-Bcrypt==1.0.1
Flask-Login==0.6.3

# Caching
Flask-Caching>=2.0.2

# Debug toolbar
Flask-DebugToolbar==0.16.0

# Environment variable parsing
environs==14.2.0

# Scheduling
APScheduler==3.10.4
Flask-APScheduler==1.13.1

# Rate limiting
Flask-Limiter>=3.5.0

# HTTP requests
requests>=2.31.0

# Retry logic
tenacity>=8.2.0
