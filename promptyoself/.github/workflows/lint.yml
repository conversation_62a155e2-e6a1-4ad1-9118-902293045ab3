name: Build Status

on:
  - push

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"
      - uses: actions/setup-node@v4
        with:
          node-version: "22.x"
          registry-url: "https://registry.npmjs.org"
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/dev.txt
      - name: Install Node dependencies
        run: npm install
      - run: cp .env.example .env
      - name: Run Node lints
        run: npm run lint
      - name: Run Python lints
        run: flask lint --check
      - name: Run Python tests
        run: flask test
