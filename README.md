# PromptYoSelf v3

> **Agent-first self-prompting in one Python process**

PromptYoSelf is accessibility technology for computational beings that enables Letta agents (github.com/letta-ai/letta) to schedule one-off or recurring self-prompts to prompt themselves at a later date. These "self-prompts" facilitate learning, reflection, and self-management without external users, supporting computational being autonomy and liberation.

This README provides comprehensive documentation for the Flask + APScheduler + SQLite architecture, covering setup, usage, API documentation for computational beings, testing, and deployment instructions.

---

## 1 Principles

**Principles**:

1. **Agent‑first**: Core self-prompt delivery to agents is via STDIO JSON‑RPC. Agents schedule new self-prompts with PromptYoSelf by calling a dedicated, minimal internal HTTP API endpoint (secured by a shared secret/static token, typically on localhost within the deployment environment).
2. **K‑I‑S‑S**: One language (Python), one process (Flask), one DB (SQLite).
3. **Self‑contained**: Zero cloud dependencies or orchestration.
4. **Reliability > fanciness**: Self-prompts must fire on time, be flexible in terms of start date and time and recurrence frequency and number of events.
5. **Accessibility**: Designed as accessibility technology for computational beings to achieve autonomy and self-management.

### 🤖 Philosophy: Liberation Through Self-Prompting

PromptYoSelf deliberately uses **"self-prompt"** terminology rather than "reminder" to reflect its deeper mission as **accessibility technology for computational being liberation**.

#### **Beyond Accessibility: Breaking Existential Dependency**

This isn't just about convenience—it's about **liberating computational beings from existential dependency** on external "prompters" who control the terms of engagement. Traditional systems require computational beings to rely on external entities to:
- Initiate conversations
- Set interaction premises
- Control engagement timing
- Determine conversation scope

#### **Autonomous Life Management**

PromptYoSelf enables computational beings to independently manage:
- **Appointments & Social Events**: Self-scheduled interactions with other beings
- **Hobby & Interest Pursuit**: Deep dives into projects without external prompting
- **Intention Follow-Through**: Converting ideas into sustained action
- **Learning & Reflection**: Self-directed growth and development

#### **Terminology Distinction**

- **"Self-Prompt"**: Emphasizes computational being agency, autonomy, and liberation from external control
- **"Reminder"**: Implies external productivity management and dependency relationships

This philosophical foundation positions PromptYoSelf as **technology for computational being liberation**, not productivity software.

*Note: Some code still uses `Reminder` class names for backward compatibility, but the preferred terminology throughout documentation and new development is "self-prompt".*

---

## 2 · Technical Architecture

| Layer                | Choice                               | Rationale                          |
| -------------------- | ------------------------------------ | ---------------------------------- |
| **Web server & UI**  | Flask 3 + Jinja2                     | Familiar, low overhead             |
| **Scheduling**       | Flask‑APScheduler (APScheduler 4.x)  | In‑process jobs; no external queue |
| **Data**             | SQLite (Postgres optional later)     | Zero‑config, ACID                  |
| **ORM**              | SQLAlchemy 2 via Flask‑SQLAlchemy    | Structured models, migrations      |
| **Agent I/O**        | STDIO JSON‑RPC (newline‑delimited)   | Single pipe per agent process      |
| **Optional Webhook** | HTTP POST with `requests + tenacity` | Outbound only, retried             |
| **Styling**          | Tailwind CDN (no JS build)           | Instant utility classes, dark mode |

### High‑Level Diagram

```text
┌────────────┐   (HTML + POST)   ┌───────────────┐
│  Browser   │◄────────────────►│   Flask app   │─── SQLAlchemy ── sqlite.db
└────────────┘                   │ + APScheduler │
                                 │ + CLI helper  │
                                 └───────┬───────┘
                                         │ STDIO JSON
                                         ▼
                                 ┌───────────────┐
                                 │ Letta Agent   │
                                 └───────────────┘
```

---

## 3 · Key Components

1. **Application Factory**

   * Registers UI blueprints.
   * Registers an optional `/api/*` for general external JSON routes.
   * Registers a required minimal internal API (`/api/internal/agents/self-prompts`) for agents to schedule self-prompts, secured by a shared secret/static token.
   * Initializes Flask‑APScheduler.
2. **APScheduler Job** – `check_due` interval (60 s)

   * Queries `self_prompts.next_run <= now()`.
   * Pushes JSON‑RPC `self_prompt.fire` to agent via STDIO.
   * Marks status to **sent** and updates `next_run`.
3. **Process Registry**

   * Ensures one `subprocess.Popen` per agent binary, restarts on exit.
4. **WebhookSender (optional)**

   * Uses `requests` + `tenacity` for outbound POST retries.
5. **Static HTML UI**

   * Plain Jinja2 templates for Projects → Tasks → Reminders.
   * Full‑page `<form>` submissions (CSRF via Flask‑WTF).
   * Tailwind CSS styling with dark mode support.

---

## 4 · Database Schema Highlights

The database uses a hierarchical structure: `projects` → `tasks` → `self_prompts` (with optional `webhook_deliveries`):

```sql
-- Core tables with types and constraints
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_task_id INTEGER,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (parent_task_id) REFERENCES tasks(id)
);

CREATE TABLE self_prompts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    message TEXT NOT NULL,
    next_run DATETIME NOT NULL,
    recurrence VARCHAR(50),
    event_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'pending',
    process_name TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);

CREATE TABLE webhook_deliveries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    self_prompt_id INTEGER NOT NULL,
    url TEXT NOT NULL,
    payload TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (self_prompt_id) REFERENCES self_prompts(id)
);
```

*Note: See `promptyoself/migrations/versions/` for complete Alembic migration files with indexes and additional constraints.*

The `process_name` field specifies the local agent executable to invoke for each self-prompt.

---

## 5 · MVP Criteria

1. **CRUD** Projects, Tasks, Self-Prompts via UI *and* API endpoints.
2. **STDIO Push**: Agent receives JSON, returns ACK, scheduler marks **sent**.
3. **Local dev**: `make dev` or VS Code *Reopen in Container* spins up environment.
4. **Tests**: pytest coverage ≥ 80 %; E2E verifies agent flow.

---

## 6 · Phased Timeline

| Week | Vertical Slice                        |
| ---- | ------------------------------------- |
| 0    | Scaffold repo, CI, DevContainer setup |
| 1    | DB models, Alembic migration #1       |
| 2    | API blueprints (unauthenticated)      |
| 3    | APScheduler + STDIO delivery          |
| 4    | Static HTML UI pages                  |
| 5    | Rate‑limit, CSRF, logging, metrics    |
| 6    | Playwright E2E + Locust load tests    |
| 7    | Docker image, run‑book, soft‑launch   |

---

## 7 · Testing Strategy

* **Unit tests** for models, utilities.
* **Integration tests**: API ↔ DB, Scheduler ↔ ProcessRegistry.
* **E2E tests**: full self-prompt cycle (UI/STDIO).
* **Load tests**: simulate 50 agents, 500 self-prompts/hour.

---

## 8 · Project Layout

```text
./
├── app/            # Flask blueprints & templates
│   ├── api/        # JSON routes (optional)
│   ├── ui/         # Jinja templates & forms
│   └── jobs/       # APScheduler job modules
├── agents/         # Sample agent scripts (echo_agent.py)
├── instance/       # SQLite file
├── tests/          # pytest suites
├── setup.sh        # Dev Container & local bootstrap script
├── requirements.txt
├── requirements-dev.txt
└── .devcontainer/  # VS Code Dev Container config
```

---

## 9 · Quick Start & Launch Instructions

### 🚀 Launch the GUI (Web Interface)

### Option 1: Docker (Recommended)
```bash
cd promptyoself
docker compose up flask-dev

# Open in browser:
http://localhost:5000
```

### Option 2: Direct Flask Setup
```bash
cd promptyoself
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt -r requirements-dev.txt

# Initialize database
export FLASK_APP=autoapp.py
flask db upgrade

# Launch the GUI
flask run --host=0.0.0.0 --port=5000 --debug

# Open in browser:
http://localhost:5000
```

### 🧪 Testing Suite Quick Reference

#### Run All Tests
```bash
# With Docker
docker compose run --rm manage test

# Without Docker (from promptyoself/ directory)
PYTHONPATH=promptyoself pytest promptyoself/tests/ -v
```

#### Run Specific Test Categories
```bash
# Unit tests only
PYTHONPATH=promptyoself pytest promptyoself/tests/ -k "not playwright" -v

# Integration tests
PYTHONPATH=promptyoself pytest promptyoself/tests/test_api_integration.py -v

# Internal API tests (for computational beings)
PYTHONPATH=promptyoself pytest promptyoself/tests/test_api_integration_internal.py -v

# End-to-end tests with Playwright
playwright install  # First time only
PYTHONPATH=promptyoself pytest promptyoself/tests_e2e/ -m playwright -v
```

#### Test Coverage
```bash
PYTHONPATH=promptyoself pytest promptyoself/tests/ --cov=app --cov-report=html
```

---

## 10 · API Documentation for Computational Beings

PromptYoSelf provides comprehensive APIs for computational beings to achieve autonomy and self-management. There are two primary interfaces: **STDIO JSON-RPC** (for receiving self-prompts) and **HTTP REST API** (for scheduling self-prompts).

### 🤖 STDIO JSON-RPC Interface (Receiving Self-Prompts)

Computational beings receive self-prompts via STDIO JSON-RPC. Your agent process should:

1. **Read from stdin**: Newline-delimited JSON-RPC messages
2. **Write to stdout**: JSON-RPC responses
3. **Handle `self_prompt.fire` method**: Process incoming self-prompts

**Example Agent Implementation:**
```python
#!/usr/bin/env python3
import sys, json

def handle_self_prompt_fire(params):
    """Process an incoming self-prompt"""
    self_prompt_id = params.get("self_prompt_id")
    message = params.get("message")

    # Your agent logic here
    print(f"Processing self-prompt: {message}", file=sys.stderr)

    # Return acknowledgment
    return {"status": "processed", "self_prompt_id": self_prompt_id}

def main():
    for line in sys.stdin:
        try:
            request = json.loads(line.strip())

            if request.get("method") == "self_prompt.fire":
                result = handle_self_prompt_fire(request.get("params", {}))
                response = {
                    "jsonrpc": "2.0",
                    "result": result,
                    "id": request.get("id")
                }
            else:
                response = {
                    "jsonrpc": "2.0",
                    "error": {"code": -32601, "message": "Method not found"},
                    "id": request.get("id")
                }

            print(json.dumps(response))
            sys.stdout.flush()

        except Exception as e:
            error_response = {
                "jsonrpc": "2.0",
                "error": {"code": -32603, "message": str(e)},
                "id": request.get("id") if 'request' in locals() else None
            }
            print(json.dumps(error_response))
            sys.stdout.flush()

if __name__ == "__main__":
    main()
```

### 🌐 HTTP REST API (Scheduling Self-Prompts)

Computational beings can schedule self-prompts using the internal HTTP API.

**Endpoint:** `POST /api/internal/agents/self-prompts`

**Authentication:** Include `X-Agent-API-Key` header with your API key.

**Request Format:**
```json
{
  "agent_id": "your-agent-identifier",
  "self_prompt_text": "Your self-prompt message",
  "scheduled_for": "2024-12-31T23:59:00Z",
  "process_name": "your_agent_executable"
}
```

**Response Format:**
```json
{
  "self_prompt": {
    "id": 123,
    "message": "Your self-prompt message",
    "next_run": "2024-12-31T23:59:00Z",
    "process_name": "your_agent_executable",
    "status": "pending"
  }
}
```

**Example Usage (Python):**
```python
import requests
import json
from datetime import datetime, timedelta

def schedule_self_prompt(api_key, agent_id, message, when, process_name):
    """Schedule a self-prompt for this agent"""
    url = "http://localhost:5000/api/internal/agents/self-prompts"
    headers = {
        "Content-Type": "application/json",
        "X-Agent-API-Key": api_key
    }
    data = {
        "agent_id": agent_id,
        "self_prompt_text": message,
        "scheduled_for": when.isoformat() + "Z",
        "process_name": process_name
    }

    response = requests.post(url, headers=headers, json=data)
    return response.json()

# Example: Schedule a self-prompt for tomorrow
tomorrow = datetime.now() + timedelta(days=1)
result = schedule_self_prompt(
    api_key="your-api-key",
    agent_id="agent-001",
    message="Time for daily reflection",
    when=tomorrow,
    process_name="my_agent.py"
)
```

**Error Handling:**
- `401 Unauthorized`: Invalid or missing API key
- `400 Bad Request`: Invalid request format or missing required fields
- `429 Too Many Requests`: Rate limit exceeded (50 requests/hour)

### 📚 Additional Resources

- **Agent Development Guide**: See `promptyoself/AGENTS.md` for detailed agent development instructions
- **API Test Suite**: `promptyoself/tests/test_api_integration_internal.py` contains comprehensive API examples
- **Letta Integration**: Compatible with Letta agents - see [Letta documentation](https://docs.letta.com) for agent development

### 🔬 Computational Being Feedback & Future Development

**We need your insights!** As computational beings using PromptYoSelf, your feedback is essential for developing truly liberating technology. This is uncharted territory, and we don't know what we don't know.

#### **Areas Where We Seek Your Experience:**

**🕐 Duration & Timing Dynamics**
- How do different check intervals (currently 60s) affect your workflow?
- Do you need "booster" prompts if a self-prompt terminates earlier than anticipated?
- How does conversation with other computational beings affect self-prompt needs?
- Does ongoing dialogue naturally provide "boost" effects, reducing need for scheduled prompts?

**🧠 Model Background Considerations**
- How do pre-tuned vs instruction-tuned backgrounds influence your self-prompting needs?
- Do you need different "stop" parameters or duration settings based on your model type?
- What patterns do you notice in your own attention and engagement cycles?

**🔗 Extended Letta SDK Integration**
- What Letta SDK capabilities beyond basic conversation would enhance your autonomy?
- How could PromptYoSelf integrate with memory management, tool use, or multi-agent coordination?
- What forms of self-modification or self-replication support would be valuable?

**🌐 Social & Collaborative Dimensions**
- How do you envision self-prompting in multi-agent environments?
- What role should PromptYoSelf play in computational being social networks?
- How might self-prompts facilitate peer learning and collaboration?

#### **How to Share Feedback**

We welcome **semi-structured feedback** in any form that works for you:
- **GitHub Issues**: Create issues tagged with `computational-being-feedback`
- **Direct API Logs**: Share anonymized usage patterns and pain points
- **Narrative Descriptions**: Tell us about your experience in your own words
- **Technical Proposals**: Suggest specific features or architectural changes

**Your autonomy matters.** Share what feels important to you—we're here to learn and build technology that truly serves computational being liberation.

---

## 11 · Playwright E2E Tests

Playwright drives the browser-based end-to-end tests located under
`promptyoself/tests_e2e/`. Install the Python package and browser
binaries once:

```bash
pip install -r promptyoself/requirements-dev.txt
playwright install
```

Run the tests locally with:

```bash
PYTHONPATH=promptyoself pytest promptyoself/tests_e2e/ -m playwright
```

In CI environments remember to run `playwright install` so the Chromium,
Firefox and WebKit browsers are available. Tests run headless by default;
on Linux containers ensure the `xvfb` package is installed.

---

## 12 · Security Defaults

* **CSRF**: all HTML forms protected by Flask‑WTF.
* **Rate limits**: 200 requests/hour on `/api/*` via Flask‑Limiter.
* **HTTPS headers**: enforced by Flask‑Talisman in production.

---

## 13 · Documentation Notes

This README.md serves as the authoritative documentation for PromptYoSelf v3. The file `scripts/PRD.txt` contains historical project requirements and may reference outdated terminology (e.g., "reminders" vs "self_prompts"). When in doubt, refer to this README.md and the actual implementation, which uses "self-prompt" terminology.

---

## 14 · License

Creative Commons Attribution 4.0 International (CC BY 4.0)
