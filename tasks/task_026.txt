# Task ID: 26
# Title: Remove Authentication API Routes and Tests
# Status: pending
# Dependencies: 14
# Priority: high
# Description: Remove or update the missing '/api/auth/login' endpoint and all related authentication API routes, and update authentication-related integration tests to align with the new unauthenticated system design. Clean up authentication blueprints, middleware, and any authentication-related API infrastructure, focusing only on authentication API code (not templates or internal endpoints).
# Details:
1. Identify all authentication-related API routes (e.g., '/api/auth/login', '/api/auth/logout', '/api/auth/*') in the Flask application and remove their route definitions, handlers, and registration from the API blueprint.
2. Remove or refactor any authentication middleware, decorators, or request hooks (e.g., @login_required, token validation) that are no longer relevant in the unauthenticated system.
3. Delete or update any authentication-related API schemas, serializers, or error handlers.
4. Audit the test suite for integration and unit tests that reference authentication API endpoints or behaviors. Remove or rewrite these tests to reflect the new unauthenticated design, ensuring no references to removed endpoints or authentication flows remain.
5. Clean up any authentication-related configuration, imports, or dependencies in the API codebase (e.g., Flask-Login, JWT, OAuth libraries if no longer used).
6. Ensure that the removal does not affect internal APIs or UI templates (these are out of scope for this task).
7. Update API documentation to remove references to authentication endpoints and flows.
8. Coordinate with the test suite maintainers to ensure all integration tests pass after these changes, and that the test coverage remains above the required threshold.

# Test Strategy:
1. Run the full API integration and unit test suite to confirm that all tests related to authentication endpoints are either removed or updated, and that no tests fail due to missing authentication routes.
2. Attempt to access any previously existing authentication API endpoints (e.g., '/api/auth/login') and verify that they return a 404 or are no longer registered.
3. Review the API blueprint and middleware code to ensure no authentication-related code remains.
4. Confirm that API documentation no longer references authentication endpoints or flows.
5. Ensure that the removal of authentication code does not impact unrelated API endpoints or internal APIs.
6. Validate that code coverage for the API remains at or above the required threshold after test updates.

# Subtasks:
## 1. Remove Authentication API Routes and Handlers [pending]
### Dependencies: None
### Description: Identify and remove all authentication-related API routes (e.g., '/api/auth/login', '/api/auth/logout', '/api/auth/*') from the Flask application, including their route definitions, handlers, and blueprint registrations.
### Details:
Search the codebase for all authentication API endpoints and remove their route decorators, handler functions, and any references in the API blueprint registration. Ensure that only authentication API routes are affected, and internal endpoints or UI templates are not modified.

## 2. Remove or Refactor Authentication Middleware and Infrastructure [pending]
### Dependencies: 26.1
### Description: Remove or refactor authentication middleware, decorators (e.g., @login_required), request hooks, and any authentication-related API infrastructure that are no longer needed in the unauthenticated system.
### Details:
Audit the codebase for authentication middleware, decorators, and request hooks used by the API (such as token validation or session checks). Remove or refactor these components, ensuring that any dependencies on authentication logic are eliminated from the API layer.

## 3. Clean Up Authentication-Related Schemas, Serializers, and Error Handlers [pending]
### Dependencies: 26.2
### Description: Delete or update any authentication-related API schemas, serializers, and error handlers that are no longer relevant after the removal of authentication routes and middleware.
### Details:
Identify all schemas, serializers, and error handlers used exclusively for authentication endpoints or flows. Remove these files or code blocks, and update any remaining code to remove references to them.

## 4. Update and Clean Up Authentication-Related Integration Tests [pending]
### Dependencies: 26.3
### Description: Audit the test suite for integration and unit tests referencing authentication API endpoints or behaviors. Remove or rewrite these tests to reflect the unauthenticated system design, ensuring no references to removed endpoints or authentication flows remain.
### Details:
Search the test suite for any tests that interact with authentication endpoints or rely on authentication logic. Remove obsolete tests and update any that require changes due to the new unauthenticated design. Ensure test coverage remains above the required threshold.

