# Task ID: 16
# Title: Define and Implement Internal API Endpoint for Agent-Scheduled Reminders
# Status: done
# Dependencies: 13
# Priority: high
# Description: Create a secure internal API endpoint that allows managed agents (such as Let<PERSON>) to programmatically schedule new reminders in PromptyoSelf. This endpoint should validate agent identity, accept reminder details, and persist reminders to the database.
# Details:
1. Design a new internal API route (e.g., POST /api/internal/agents/reminders) within a dedicated blueprint or under an 'internal' namespace to separate it from public endpoints.
2. Implement authentication and authorization for managed agents, using API keys, JWTs, or another secure mechanism. Only pre-approved agents should be able to access this endpoint.
3. Define the expected JSON payload (e.g., {"agent_id": ..., "reminder_text": ..., "scheduled_for": ..., "process_name": ...}) and validate all required fields, including process_name (NOT NULL constraint).
4. Integrate with the existing Reminder SQLAlchemy model to create and persist new reminders. Ensure that reminders created via this endpoint are indistinguishable from those created via the UI/API, except for agent attribution.
5. Add robust error handling for invalid input, unauthorized access, and database errors. Return appropriate HTTP status codes and error messages.
6. Update API documentation to include this new endpoint, its authentication requirements, and example requests/responses.
7. (Optional) Add rate limiting or logging for agent activity to monitor usage and prevent abuse.

# Test Strategy:
1. Attempt to create reminders via the new endpoint using valid and invalid agent credentials; verify only authorized agents can schedule reminders.
2. Submit valid and invalid payloads; confirm that reminders are created in the database with correct fields, and that invalid requests return appropriate error messages and status codes.
3. Check that reminders scheduled via the agent endpoint appear in the UI and are indistinguishable from user-created reminders, except for agent attribution if applicable.
4. Review API documentation for completeness and accuracy.
5. (Optional) Simulate high-frequency requests to verify rate limiting or logging if implemented.

# Subtasks:
## 1. Design the API Route and Namespace [done]
### Dependencies: None
### Description: Determine the appropriate URL structure and namespace for the agent-scheduled reminders endpoint, ensuring it aligns with internal API conventions.
### Details:
Specify HTTP method(s), endpoint path (e.g., /internal/agents/reminders), and versioning if applicable.
<info added on 2025-05-28T18:41:42.057Z>
Implementation Plan for Internal API Endpoint

1. Create a new blueprint file at promptyoself/app/api/internal.py to serve as the namespace for internal APIs, using the URL prefix /api/internal.
2. Define a POST endpoint at /agents/reminders within this blueprint, resulting in the full route /api/internal/agents/reminders.
3. In promptyoself/app/api/internal.py, define the blueprint:
   blueprint = Blueprint("internal_api", __name__, url_prefix="/api/internal")
4. Add the route with rate limiting:
   @blueprint.route("/agents/reminders", methods=["POST"])
   @limiter.limit("50 per hour")
   def create_agent_reminder():
       return jsonify({"message": "Internal agent reminder endpoint created"}), 201
5. Update promptyoself/app/__init__.py to import the new blueprint and register it in the register_blueprints() function:
   from app.api import internal as api_internal
   app.register_blueprint(api_internal.blueprint)
This approach maintains consistency with the existing application structure and ensures a clear separation for internal endpoints.
</info added on 2025-05-28T18:41:42.057Z>

## 2. Implement Agent Authentication and Authorization [done]
### Dependencies: 16.1
### Description: Set up mechanisms to authenticate agents and verify their permissions to schedule reminders via the internal API.
### Details:
Integrate with existing authentication systems (e.g., JWT, OAuth) and enforce role-based access control.
<info added on 2025-05-28T18:43:55.787Z>
Implement API key-based authentication for the internal agent reminder endpoint. Store the API key in an environment variable named INTERNAL_AGENT_API_KEY, following existing application patterns for secret management. Add logic to extract the API key from the 'X-Agent-API-Key' header in incoming requests and compare it to the configured value. If the API key is missing or invalid, return a 401 Unauthorized response with an appropriate error message and log the failure for security monitoring. Create a decorator function (require_agent_api_key) to encapsulate this logic and apply it to the create_agent_reminder endpoint. Update .env.example to include documentation for the new environment variable.
</info added on 2025-05-28T18:43:55.787Z>

## 3. Define and Validate the JSON Payload [done]
### Dependencies: 16.2
### Description: Specify the required and optional fields for the reminder scheduling payload and implement validation logic.
### Details:
Create a schema for the payload (e.g., reminder time, message, agent ID) and handle invalid or missing data.
<info added on 2025-05-28T18:47:27.284Z>
# Implementation Plan for JSON Payload Validation

## JSON Payload Structure
Based on the parent task requirements, I'll define the following JSON payload structure:
{
  "agent_id": "string/int",      // ID of the agent scheduling the reminder
  "reminder_text": "string",    // Text content of the reminder (required, non-empty)
  "scheduled_for": "string",   // ISO 8601 formatted datetime when reminder should trigger
  "process_name": "string"     // Process name (required, non-empty)
}

## Validation Approach
After examining the codebase, I see two potential approaches for validation:
1. Using Flask-WTF forms (similar to how the UI validation works)
2. Direct dictionary validation in the route function

For this internal API endpoint, I'll implement direct dictionary validation since:
- The payload fields differ from the existing ReminderForm
- It's an internal API with specific requirements
- This approach is more straightforward for API-specific validation

## Validation Checks
1. Check if the request contains valid JSON data
2. Validate the presence of all required fields
3. Validate field types and formats:
   - `agent_id`: Must be present (string or int)
   - `reminder_text`: Must be a non-empty string
   - `scheduled_for`: Must be a valid ISO 8601 datetime string
   - `process_name`: Must be a non-empty string (explicit NOT NULL check)
4. Return descriptive error messages for each validation failure

## Error Handling Strategy
- Return HTTP 400 Bad Request for validation failures
- Include a JSON response with an "error" key and detailed message
- Specify which field failed validation and why
- For successful validation, return HTTP 200 with success message (actual DB integration will be in subtask 16.4)

I'll implement this in the `create_agent_reminder()` function in `promptyoself/app/api/internal.py`.
</info added on 2025-05-28T18:47:27.284Z>

## 4. Integrate with the Reminder Model for Persistence [done]
### Dependencies: 16.3
### Description: Connect the API endpoint to the Reminder model to store scheduled reminders in the database.
### Details:
Ensure reminders are saved with all necessary attributes and linked to the scheduling agent.
<info added on 2025-05-28T18:49:38.202Z>
Implementation Plan for Integrating with Reminder Model:

1. Map the incoming payload fields to the Reminder model:
   - Map `reminder_text` from the payload to the Reminder's `message` field.
   - Map `scheduled_for` (already converted to a datetime object) to the Reminder's `next_run` field.
   - Map `process_name` from the payload to the Reminder's `process_name` field.

2. Address the required `task_id` field:
   - Since the Reminder model requires a non-null `task_id` and the payload does not provide it, retrieve or create a dedicated 'System Task' to associate with all agent-scheduled reminders.
   - Use the Task model to look up this system task by a unique identifier or name; if it does not exist, create it.

3. Implementation steps:
   - Import the Reminder and Task models.
   - After payload validation, ensure the system task exists and retrieve its `id`.
   - Create a new Reminder instance with the mapped fields and associate it with the system task's `id`.
   - Set default values for required fields not provided in the payload, such as `status` set to 'pending' and `event_count` set to 0.
   - Save the new Reminder to the database using the CRUDMixin methods (`Reminder.create(**kwargs)` or by instantiating and calling `save()`).
   - Return a success response containing the details of the created reminder.

4. Ensure all database operations utilize the existing CRUDMixin functionality for consistency and maintainability.
</info added on 2025-05-28T18:49:38.202Z>

## 5. Add Error Handling and Appropriate HTTP Responses [done]
### Dependencies: 16.4
### Description: Implement robust error handling for common failure scenarios and return meaningful HTTP status codes and messages.
### Details:
Handle authentication failures, validation errors, and database issues gracefully.
<info added on 2025-05-28T18:51:29.784Z>
Enhance error handling in the create_agent_reminder endpoint by implementing specific exception handling for SQLAlchemy errors: catch IntegrityError (return 409 Conflict), DataError (return 422 Unprocessable Entity), OperationalError (return 503 Service Unavailable), SQLAlchemyError (return 500 Internal Server Error), and a final catch-all for unexpected exceptions (also 500). In each database-related exception handler, ensure db.session.rollback() is called to maintain consistency. All error responses should use a consistent JSON format {"error": "Human-readable error message"} without exposing sensitive system details. Log detailed error information for debugging, including stack traces in development. Replace any generic exception handling with these specific handlers and maintain consistent logging throughout the implementation.
</info added on 2025-05-28T18:51:29.784Z>
<info added on 2025-05-28T18:52:07.124Z>
Implementation is complete. The create_agent_reminder endpoint now includes specific exception handlers for IntegrityError (409), DataError (422), OperationalError (503), SQLAlchemyError (500), and a final Exception catch-all (500). Each handler performs db.session.rollback() as needed, with an additional check for active sessions in the generic handler. All error responses use a consistent JSON format with user-friendly messages, and detailed error logging (including stack traces) is in place for debugging. SQLAlchemy exception imports have been added, and the error handling aligns with Flask best practices.
</info added on 2025-05-28T18:52:07.124Z>

## 6. Update API Documentation [done]
### Dependencies: 16.5
### Description: Document the new endpoint, including route, authentication requirements, payload schema, and possible responses.
### Details:
Update internal API docs or Swagger/OpenAPI specs as appropriate.
<info added on 2025-05-28T19:57:41.450Z>
Add a new "API Documentation" section to the README.md file before the "Asset Management" section. This section should include:

- A general introduction to the internal API.
- An "Internal API Endpoints" subsection.
- Comprehensive documentation for the POST /api/internal/agents/reminders endpoint, covering:
  - Endpoint description and intended use.
  - Required authentication via the X-Agent-API-Key header.
  - Detailed request payload schema listing all required fields.
  - Example curl command demonstrating a valid request.
  - Example of a successful 201 Created response in JSON format.
  - Example error responses for status codes 400, 401, 409, 422, 503, and 500, each with sample JSON bodies.
</info added on 2025-05-28T19:57:41.450Z>

## 7. Implement Rate Limiting or Logging for Agent Activity (Optional) [done]
### Dependencies: 16.6
### Description: Add rate limiting or logging to monitor and control agent usage of the reminder scheduling endpoint.
### Details:
Configure rate limits per agent or log activity for auditing and troubleshooting.
<info added on 2025-05-28T19:59:46.496Z>
Enhance logging to consistently include the agent_id in all relevant log messages, ensuring comprehensive monitoring of agent activity and usage for auditing and troubleshooting purposes. Review all log statements within the endpoint to verify that agent_id is present wherever applicable, especially in success, error, and validation logs.
</info added on 2025-05-28T19:59:46.496Z>
<info added on 2025-05-28T20:00:17.768Z>
Implementation Updates

Enhanced the logging in the agent reminder endpoint to consistently include agent_id in all relevant log messages:

1. Updated the success log message to include agent_id:
   - Changed from logging only the reminder ID to logging both agent_id and reminder ID when a new reminder is created.

2. Added agent_id to all error log messages using a safe pattern:
   - Incorporated agent_id into database integrity, data, operational, general SQLAlchemy, and unexpected error logs.
   - Used a defensive approach with data.get('agent_id', 'unknown') to ensure agent_id is logged even if not present in the data dictionary.

These improvements provide comprehensive monitoring of agent activity, facilitate tracking and auditing, and support troubleshooting agent-specific issues. The existing rate limiting (50 requests per hour) remains in place and meets the requirements for abuse prevention.
</info added on 2025-05-28T20:00:17.768Z>

