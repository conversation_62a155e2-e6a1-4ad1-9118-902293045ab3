# Task ID: 7
# Title: Develop STDIO JSON-RPC Agent Communication System
# Status: done
# Dependencies: 6
# Priority: high
# Description: Implement STDIO JSON-RPC interface for agent communication, including process registry, lifecycle management, reliable message delivery with ACKs, timeout and error handling, scheduler integration, and comprehensive logging.
# Details:
- The system now includes a ProcessRegistry class for managing subprocesses for each agent binary, ensuring only one process per agent is running and restarting on exit.
- JSON-RPC message delivery is implemented over STDIO, with synchronous ACK handling, timeouts, and robust error management.
- The ProcessManager handles agent lifecycle (start, stop, monitor, restart) and integrates with the ProcessRegistry.
- Scheduler integration ensures periodic monitoring and reminder delivery via JSON-RPC, with fallback to STDOUT if needed.
- Comprehensive logging is present across all components for process management, message delivery, ACKs, errors, and scheduler activities.
- All core features (agent registration, lifecycle, message delivery, ACKs, error handling, scheduler integration, logging) are complete and verified.

# Test Strategy:
All features have been implemented and verified through unit and integration tests:
- ProcessRegistry: tested for process registration, lookup, removal, and thread safety.
- ProcessManager: tested for agent start, stop, restart, and monitoring, including health checks and restart limits.
- JSON-RPC delivery: tested for correct serialization, delivery, ACK receipt, and error/timeout handling with simulated agent processes.
- Scheduler integration: tested for correct periodic monitoring and reminder delivery.
- Logging: verified for all key events, errors, and state changes.
- Multi-agent scenarios tested for concurrency and robustness.

# Subtasks:
## 1. Design and Implement ProcessRegistry Class [done]
### Dependencies: None
### Description: Create the ProcessRegistry class to manage process registration, lookup, and removal.
### Details:
Define data structures for storing process information, implement methods for adding, retrieving, and removing processes, and ensure thread safety if required.
<info added on 2025-05-27T12:07:12.556Z>
Implemented the ProcessRegistry class in promptyoself/app/agents/process_registry.py with methods for registering, retrieving, unregistering, and listing processes. Utilized threading.Lock to ensure thread safety. Defined a ProcessInfo TypedDict to structure process information. Incorporated basic logging for key operations. Included a simple test block under if __name__ == '__main__'. Resolved Pylance type errors by specifying subprocess.Popen[bytes] and ProcessInfo for dictionary values, and List[Dict[str, Any]] for the return type of list_processes, ensuring the Popen object is not exposed.
</info added on 2025-05-27T12:07:12.556Z>

## 2. Develop Process Lifecycle Management [done]
### Dependencies: 7.1
### Description: Implement mechanisms to start, stop, and monitor processes.
### Details:
Integrate with the ProcessRegistry to update process states, handle process creation and termination, and monitor health/status.
<info added on 2025-05-27T12:13:14.309Z>
Implemented the ProcessManager class in promptyoself/app/agents/process_manager.py with methods for starting (start_agent), stopping (stop_agent), and checking the status (check_agent_status) of agent processes. Integrated ProcessManager with ProcessRegistry for process state updates, creation, and termination. Added a basic monitor_and_restart_agents method for monitoring and automatic restart of agents. Exported ProcessManager and ProcessDetails from promptyoself/app/agents/__init__.py. Core logic for managing the process lifecycle is now established.
</info added on 2025-05-27T12:13:14.309Z>

## 3. Implement JSON-RPC Message Delivery [done]
### Dependencies: 7.2
### Description: Enable sending and receiving JSON-RPC messages between processes.
### Details:
Establish communication channels, serialize/deserialize JSON-RPC messages, and route messages to the correct process using the registry.
<info added on 2025-05-27T12:18:20.307Z>
Implemented JSON-RPC message delivery in ProcessManager. Added jsonrpc_utils.py with helper functions for creating, serializing, and deserializing JSON-RPC messages, all with type hints, and exported these helpers in the agents package. Refactored ProcessManager to use ProcessDetails from ProcessRegistry, updating agent lifecycle methods accordingly. Introduced send_jsonrpc_message(agent_name, method, params, message_id) to construct, serialize, and send JSON-RPC requests to agent processes via stdin, with comprehensive error handling and logging for message delivery and failures.
</info added on 2025-05-27T12:18:20.307Z>

## 4. Handle ACKs for Message Delivery [done]
### Dependencies: 7.3
### Description: Implement acknowledgment (ACK) handling for reliable message delivery.
### Details:
Track sent messages, await ACKs, and retransmit or flag errors if ACKs are not received within a timeout.
<info added on 2025-05-27T15:39:47.505Z>
Updated ProcessManager.send_jsonrpc_message to synchronously wait up to 5 seconds for an ACK response from the agent's stdout, matching the original message_id. The method logs successful ACKs, timeouts, and any parsing or I/O errors encountered. It returns the parsed ACK response object if successful, or None if a timeout or error occurs. Existing background ACK monitoring and retry mechanisms are retained to ensure robustness.
</info added on 2025-05-27T15:39:47.505Z>

## 5. Implement Timeout and Error Management [done]
### Dependencies: 7.4
### Description: Add mechanisms to handle timeouts and errors in process management and message delivery.
### Details:
Detect and respond to timeouts, log errors, and trigger recovery or notification mechanisms as needed.
<info added on 2025-05-27T15:51:19.556Z>
Enhanced ProcessManager to include agent startup timeouts and implemented constants for timeouts and retry limits. Refined error handling in send_jsonrpc_message, ensuring robust exception catching and improved logging. Added basic health checks and enforced restart limits within monitor_and_restart_agents to prevent excessive restarts. Improved error handling for Popen interactions, including comprehensive None checks before accessing Popen attributes and addressing Pylance typing issues related to Popen.pid.
</info added on 2025-05-27T15:51:19.556Z>

## 6. Integrate with Scheduler [done]
### Dependencies: 7.5
### Description: Connect process management and message delivery with the system scheduler.
### Details:
Ensure processes are scheduled appropriately, and message delivery respects scheduling constraints and priorities.
<info added on 2025-05-27T15:55:10.218Z>
Added a scheduled 'monitor_agents_job' to periodically monitor agent processes, performing a single monitoring cycle each run. Integrated ProcessManager.send_jsonrpc_message into the 'deliver_reminder' job to attempt JSON-RPC delivery for reminders with a 'process_name' attribute, falling back to STDOUT if absent or if JSON-RPC delivery fails. Implemented logging for agent monitoring activities and for all JSON-RPC delivery attempts, including successes and failures.
</info added on 2025-05-27T15:55:10.218Z>

## 7. Implement Logging for All Components [done]
### Dependencies: 7.6
### Description: Add comprehensive logging for process management, message delivery, ACKs, errors, and scheduler integration.
### Details:
Ensure logs capture key events, errors, and state changes for debugging and monitoring purposes.
<info added on 2025-05-27T15:57:11.807Z>
Reviewed logging for agent components (ProcessRegistry, ProcessManager, and agent-related parts of reminder_jobs.py). Existing logging is comprehensive, consistent, and meets all requirements for tracking agent lifecycle, message delivery, acknowledgments, errors, and scheduler integration. No additional logging changes were necessary.
</info added on 2025-05-27T15:57:11.807Z>

