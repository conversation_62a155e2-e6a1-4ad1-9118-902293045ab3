{"name": "promptyoself-v3", "version": "1.0.0", "description": "PromptYoSelf V3 - AI-powered reminder and task management system", "dependencies": {"task-master-ai": "^0.15.0", "@modelcontextprotocol/server-sequential-thinking": "latest"}, "devDependencies": {"@smithery/cli": "latest"}, "scripts": {"install-global": "npm install -g task-master-ai @modelcontextprotocol/server-sequential-thinking @smithery/cli", "verify-tools": "npx --version && npm --version", "test-mcp": "npx -y @smithery/cli@latest run @xinzhongyouhai/mcp-sequentialthinking-tools --help"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}