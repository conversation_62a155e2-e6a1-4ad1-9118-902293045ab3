# DevContainer Setup Options

This directory contains two complete devcontainer configurations for different environments:

1. **Standard Environment** (your current system with Docker networking issues)
2. **OpenAI Codex Platform** (colleague's system with Node.js pre-installed)

## Current Configuration

The default `devcontainer.json` is configured for standard environments that need Node.js installation and use host networking to work around Docker bridge network issues.

## Available Configurations

### 1. `devcontainer.json` + `setup.sh` (Default - Standard Environment)
**Best for**: Your current system and similar environments with Docker networking issues.

**Features**:
- ✅ Installs Node.js from NodeSource repository
- ✅ Uses `--network=host` to work around Docker bridge network issues
- ✅ Full setup with all dependencies
- ✅ Handles network connectivity issues gracefully
- ✅ Includes comprehensive error handling and retries

**Use this when**:
- Working on systems with Docker networking issues
- Need to install Node.js in the container
- Using standard Docker environments

### 2. `devcontainer-codex.json` + `setup-no-node.sh` (OpenAI Codex)
**Best for**: OpenAI Codex platform and other environments with Node.js pre-installed.

**Features**:
- ✅ Assumes Node.js is pre-installed
- ✅ No `--network=host` needed (normal Docker networking)
- ✅ Optimized for faster setup (skips Node.js installation)
- ✅ Early Node.js verification with helpful error messages
- ✅ Cleaner configuration for cloud platforms

**Use this when**:
- Working on OpenAI Codex platform
- Using environments with Node.js pre-installed
- Want faster container startup times
- Don't have Docker networking issues

## How to Switch Between Configurations

### For Standard Environment (Default):
Use the existing `devcontainer.json` - no changes needed.

### For OpenAI Codex Platform:
1. **Rename current config**: `mv devcontainer.json devcontainer-standard.json`
2. **Activate Codex config**: `mv devcontainer-codex.json devcontainer.json`
3. **Rebuild container** in VS Code

### To Switch Back to Standard:
1. **Rename Codex config**: `mv devcontainer.json devcontainer-codex.json`
2. **Restore standard config**: `mv devcontainer-standard.json devcontainer.json`
3. **Rebuild container** in VS Code

### Alternative: Use VS Code DevContainer Selection
You can also keep both files and use VS Code's "Dev Containers: Rebuild and Reopen in Container" command with a specific config file.

## Network Connectivity Benefits

Both scripts now include improved network connectivity handling:

- ✅ Early network connectivity checks
- ✅ Graceful degradation when network is unavailable
- ✅ Retry mechanisms for package installations
- ✅ Clear status reporting

## Troubleshooting

### If Node.js is missing:
1. Check which setup script is being used
2. Verify your base image includes Node.js
3. If needed, switch to the full `setup.sh` and uncomment Node.js installation

### If network issues persist:
1. Both scripts will continue with offline-capable operations
2. You can re-run the setup script manually after network is restored
3. Individual package installations can be run manually

### If container build fails:
1. The current configuration should work with host networking
2. No features require internet access during build phase
3. All network-dependent operations happen in postCreateCommand

## Files Modified for Docker Networking Fix

1. **devcontainer.json**: Added `--network=host` to runArgs
2. **setup-with-node.sh**: New optimized script assuming Node.js pre-installed
3. **setup.sh**: Modified to comment out Node.js installation
4. **This README**: Documentation of options

## Recommended Approach

1. **Try current setup first**: The `setup-with-node.sh` should work for most cases
2. **If Node.js is missing**: Switch to `setup.sh` and uncomment Node.js installation
3. **For custom needs**: Modify either script as needed for your specific environment

The current configuration prioritizes:
- ✅ Fast container startup
- ✅ Network connectivity compatibility
- ✅ Minimal external dependencies during build
- ✅ Clear error messages and fallback options
