# Quick Setup Guide

Choose your environment and follow the appropriate setup:

## 🖥️ Standard Environment (Your Current System)

**Use this if**: You have Docker networking issues or need Node.js installed in the container.

### Files Used:
- `devcontainer.json` (default)
- `setup.sh`

### Features:
- ✅ Installs Node.js automatically
- ✅ Uses host networking to bypass Docker bridge issues
- ✅ Full dependency installation

### Setup:
1. Open project in VS Code
2. Use "Reopen in Container" 
3. Wait for setup to complete

---

## ☁️ OpenAI Codex Platform

**Use this if**: Working on OpenAI Codex or similar platforms with Node.js pre-installed.

### Files Used:
- `devcontainer-codex.json`
- `setup-no-node.sh`

### Features:
- ✅ Assumes Node.js is pre-installed
- ✅ Faster setup (no Node.js installation)
- ✅ Standard Docker networking
- ✅ Optimized for cloud platforms

### Setup:
1. **Switch to Codex configuration**:
   ```bash
   cd .devcontainer
   mv devcontainer.json devcontainer-standard.json
   mv devcontainer-codex.json devcontainer.json
   ```

2. **Open in VS Code and rebuild**:
   - Use "Dev Containers: Rebuild and Reopen in Container"

3. **Wait for setup to complete**

---

## 🔄 Switching Between Environments

### To OpenAI Codex:
```bash
cd .devcontainer
mv devcontainer.json devcontainer-standard.json
mv devcontainer-codex.json devcontainer.json
```

### Back to Standard:
```bash
cd .devcontainer
mv devcontainer.json devcontainer-codex.json
mv devcontainer-standard.json devcontainer.json
```

After switching, always use "Dev Containers: Rebuild and Reopen in Container" in VS Code.

---

## 🛠️ File Overview

| File | Purpose | Environment |
|------|---------|-------------|
| `devcontainer.json` | Standard config with host networking | Your system |
| `devcontainer-codex.json` | Codex config with normal networking | OpenAI Codex |
| `setup.sh` | Full setup with Node.js installation | Standard |
| `setup-no-node.sh` | Optimized setup, assumes Node.js exists | Codex |

---

## 🚨 Troubleshooting

### If Node.js is missing on Codex:
The setup will detect this and show clear error messages. Switch back to standard configuration.

### If networking fails on standard:
The setup includes retry mechanisms and will continue with offline-capable operations.

### If container build fails:
- Check which configuration you're using
- Ensure you're using the right setup for your environment
- Check the logs for specific error messages

---

## ✅ Verification

After setup completes, you should see:
- ✅ Python virtual environment activated
- ✅ Node.js and npm available
- ✅ All project dependencies installed
- ✅ Database initialized
- ✅ Tests passing (if applicable)

The setup script will show a final status summary with versions of all tools.
