{"name": "PromptYoSelf Dev", "image": "mcr.microsoft.com/devcontainers/python:3.12-bookworm", "forwardPorts": [5000, 8811, 8932], "runArgs": ["--init", "--network=host"], "workspaceMount": "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached", "workspaceFolder": "/workspace", "postCreateCommand": "bash .devcontainer/setup.sh", "containerEnv": {"FLASK_APP": "promptyoself.autoapp:app", "FLASK_ENV": "development", "DATABASE_URL": "sqlite:///instance/dev.sqlite3", "PYTHONPATH": "/workspace", "NODE_ENV": "development"}, "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "charliermarsh.ruff", "ms-azuretools.vscode-docker", "kilocode.kilo-code", "Augment.vscode-augment"], "settings": {"python.defaultInterpreterPath": "/workspace/.venv/bin/python", "editor.formatOnSave": true, "python.analysis.typeCheckingMode": "strict", "terminal.integrated.defaultProfile.linux": "bash"}}}, "remoteUser": "vscode"}