#!/usr/bin/env bash
# SPDX-License-Identifier: MIT
# -------------------------------------------------------------
# Bootstrap script executed by the devcontainer's postCreate step.
# Mirrors container setup when run locally.
# -------------------------------------------------------------
set -uo pipefail  # Removed -e to allow script to continue on errors

printf "\n🔧  Setting up PromptYoSelf development environment…\n\n"

# Check network connectivity first
echo "🌐  Checking network connectivity..."
NETWORK_OK=false
if ping -c 1 ******* >/dev/null 2>&1; then
  echo "✅  Network connectivity is working"
  NETWORK_OK=true
else
  echo "⚠️  Network connectivity issues detected. Will skip network-dependent installations."
  echo "🔧  You can run this script again later when network is available."
fi

# 1. System-level dependencies -------------------------------------------------
echo "🔧  Installing system dependencies..."

# Check network connectivity first
echo "🌐  Checking network connectivity..."
if ! ping -c 1 ******* >/dev/null 2>&1; then
  echo "⚠️  Network connectivity issues detected. Attempting to continue..."
  # Try to fix DNS resolution
  echo "nameserver *******" | sudo tee -a /etc/resolv.conf >/dev/null
  echo "nameserver *******" | sudo tee -a /etc/resolv.conf >/dev/null
fi

# Update package lists with retries
echo "📦  Updating package lists..."
for i in {1..3}; do
  if sudo apt-get update; then
    echo "✅  Package lists updated successfully"
    break
  else
    echo "⚠️  Attempt $i failed, retrying in 5 seconds..."
    sleep 5
  fi
done

# Install essential packages first
echo "🔧  Installing essential build tools..."
sudo apt-get install -y --no-install-recommends build-essential curl ca-certificates || echo "⚠️  Some essential packages failed to install"

# Install database tools
echo "🗄️  Installing database tools..."
sudo apt-get install -y --no-install-recommends sqlite3 libsqlite3-dev || echo "⚠️  Database tools installation had issues"

# Install monitoring tools (optional)
echo "📊  Installing monitoring tools..."
sudo apt-get install -y --no-install-recommends htop sysstat net-tools iotop || echo "⚠️  Monitoring tools installation had issues"

# Install Playwright dependencies (optional, for browser testing)
echo "🌐  Installing browser dependencies for Playwright..."
sudo apt-get install -y --no-install-recommends \
libatk1.0-0 \
libatk-bridge2.0-0 \
libcups2 \
libxkbcommon0 \
libatspi2.0-0 \
libxcomposite1 \
libxdamage1 \
libxfixes3 \
libxrandr2 \
libgbm1 \
libpango-1.0-0 \
libcairo2 \
libasound2 || echo "⚠️  Some browser dependencies failed to install - Playwright may not work fully"

# Install Node.js LTS
echo "📦  Installing Node.js LTS..."
if command -v node >/dev/null 2>&1 && command -v npm >/dev/null 2>&1; then
  echo "✅  Node.js already installed: $(node --version), npm: $(npm --version)"
else
  echo "🔧  Installing Node.js from NodeSource repository..."
  if curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -; then
    if sudo apt-get install -y nodejs; then
      echo "✅  Node.js installed successfully"
    else
      echo "⚠️  Node.js installation failed, but continuing..."
    fi
  else
    echo "⚠️  Failed to add Node.js repository, but continuing..."
  fi
fi

# 2. Python virtual environment -----------------------------------------------
if [ ! -d "/workspace/.venv" ]; then
  echo "🐍  Creating Python virtual environment..."
  python3 -m venv /workspace/.venv
fi

# Activate virtual environment for this session
# shellcheck disable=SC1091
source /workspace/.venv/bin/activate

# Add smart virtual environment activation to .bashrc
echo "🔧  Configuring automatic virtual environment activation..."
if ! grep -q "# Auto-activate virtual environment" ~/.bashrc; then
  cat >> ~/.bashrc << 'EOF'

# Auto-activate virtual environment (only if not already active)
if [ -f "/workspace/.venv/bin/activate" ] && [ -z "$VIRTUAL_ENV" ]; then
    source /workspace/.venv/bin/activate
fi
EOF
fi

# Set PYTHONPATH
export PYTHONPATH="/workspace:${PYTHONPATH:-}"

# 3. Python tooling ------------------------------------------------------------
python -m pip install --upgrade pip

# 4. Project dependencies ------------------------------------------------------
if [ -f "requirements.txt" ]; then
  pip install -r requirements.txt
fi
if [ -f "promptyoself/requirements.txt" ]; then
  echo "🐍  Installing Flask and production dependencies..."
  pip install -r promptyoself/requirements.txt
fi
if [ -f "requirements-dev.txt" ]; then
  pip install -r requirements-dev.txt
fi
if [ -f "promptyoself/requirements-dev.txt" ]; then
  echo "🧪  Installing development dependencies (flake8, pytest, etc.)..."
  pip install -r promptyoself/requirements-dev.txt
fi

# Ensure testing dependencies are available
pip install webtest

# Install load testing dependencies (Playwright is now in requirements-dev.txt)
echo "🧪  Installing load testing dependencies..."
pip install locust

# Install Playwright browsers and dependencies
echo "🌐  Installing Playwright browsers and dependencies..."
if command -v playwright >/dev/null 2>&1; then
  echo "🎭  Installing Playwright browsers..."
  playwright install || echo "⚠️  Playwright browser installation failed"

  echo "🎭  Installing Playwright system dependencies..."
  sudo playwright install-deps || echo "⚠️  Playwright system dependencies installation failed"
else
  echo "⚠️  Playwright not found, skipping browser installation"
fi

# Install system monitoring tools for load testing
# These are now installed via devcontainer features in devcontainer.json

# 4. Environment variables for Flask ------------------------------------------
# Rely on containerEnv from devcontainer.json for FLASK_APP
# export FLASK_APP="app:create_app"
export FLASK_ENV=${FLASK_ENV:-development}
export DATABASE_URL=${DATABASE_URL:-sqlite:///instance/dev.sqlite3}

# 5. Database initialization ---------------------------------------------------
mkdir -p instance
if command -v flask >/dev/null 2>&1; then
  echo "📜  Running database migrations…"
  flask db upgrade || echo "⚠️  Alembic not configured yet – skipping upgrade"
fi

# 6. Smoke tests ---------------------------------------------------------------
if command -v pytest >/dev/null 2>&1; then
  echo "🧪  Running test suite (quiet)…"
  pytest -q || echo "⚠️  Tests failed – investigate before pushing commits"
fi

# 7. Node.js and global npm packages -------------------------------------------
if command -v npm >/dev/null 2>&1; then
  echo "📦  Installing global npm packages…"
  sudo npm install -g task-master-ai @modelcontextprotocol/server-sequential-thinking @smithery/cli

  # Ensure npx is properly available and test it
  echo "🔧  Verifying npx installation and functionality..."
  if command -v npx >/dev/null 2>&1; then
    echo "✅  npx is available at: $(which npx)"
    echo "📋  npx version: $(npx --version)"

    # Test npx functionality with a simple command
    echo "🧪  Testing npx functionality..."
    if npx --help >/dev/null 2>&1; then
      echo "✅  npx is working correctly"
    else
      echo "⚠️  npx command failed. Attempting to fix..."
      # Try to reinstall npm to ensure npx is properly linked
      sudo npm install -g npm@latest
    fi
  else
    echo "⚠️  npx not found. Installing/fixing npx..."
    # Ensure npx is available by reinstalling npm
    sudo npm install -g npm@latest

    # Verify again
    if command -v npx >/dev/null 2>&1; then
      echo "✅  npx successfully installed"
    else
      echo "❌  Failed to install npx. Manual intervention may be required."
    fi
  fi

  # Add npm global bin to PATH if not already there
  NPM_GLOBAL_BIN=$(npm config get prefix)/bin
  if [[ ":$PATH:" != *":$NPM_GLOBAL_BIN:"* ]]; then
    echo "🔧  Adding npm global bin to PATH..."
    echo "export PATH=\"$NPM_GLOBAL_BIN:\$PATH\"" >> ~/.bashrc
    export PATH="$NPM_GLOBAL_BIN:$PATH"
  fi
else
  echo "⚠️  npm not found. Skipping global npm package installation."
fi

# 8. Test specific npx commands -----------------------------------------------
echo "🧪  Testing specific npx commands..."
if command -v npx >/dev/null 2>&1; then
  echo "🔧  Testing @smithery/cli availability..."
  if npx @smithery/cli --help >/dev/null 2>&1; then
    echo "✅  @smithery/cli is working"
  else
    echo "⚠️  @smithery/cli test failed, but npx should still work for your command"
  fi

  echo "📋  You can now run your command:"
  echo "    npx -y @smithery/cli@latest run @xinzhongyouhai/mcp-sequentialthinking-tools --key 21d1f17a-a25b-45e5-a925-79906dc45ced"
else
  echo "❌  npx still not available. Please check the setup manually."
fi

# 9. Final status check -------------------------------------------------------
echo "🔍  Final status check..."
echo "📋  Environment Summary:"
echo "  🐍 Python: $(python3 --version 2>/dev/null || echo 'Not available')"
echo "  📦 Node.js: $(node --version 2>/dev/null || echo 'Not available')"
echo "  📦 npm: $(npm --version 2>/dev/null || echo 'Not available')"
echo "  🚀 npx: $(npx --version 2>/dev/null || echo 'Not available')"
echo "  🧪 pytest: $(pytest --version 2>/dev/null | head -1 || echo 'Not available')"
echo "  🎭 playwright: $(playwright --version 2>/dev/null || echo 'Not available')"

printf "\n✅  Setup complete! Some components may have failed due to network issues, but core functionality should work.\n"
printf "🔧  If you encounter issues, try running individual commands manually.\n\n"