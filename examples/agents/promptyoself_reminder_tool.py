#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Letta Custom Tool for PromptyoSelf Reminder Scheduling

This script defines a Letta custom tool that allows agents to schedule reminders
using the PromptyoSelf internal reminder API.

Usage:
    This script should be imported and the tool function should be registered
    with a Letta agent to enable reminder scheduling capabilities.

Requirements:
    - requests library for HTTP calls
    - INTERNAL_AGENT_API_KEY environment variable set
    - PromptyoSelf server running and accessible
"""

import os
import json
import requests
from datetime import datetime
from typing import Optional


def schedule_promptyoself_reminder(
    reminder_text: str,
    scheduled_for: str,
    process_name: str,
    agent_id: Optional[str] = "letta_agent",
    promptyoself_port: Optional[int] = 5000
) -> str:
    """
    Schedule a reminder using the PromptyoSelf internal API.
    
    This tool allows Letta agents to create reminders that will be processed
    by the PromptyoSelf system at the specified time.
    
    Args:
        reminder_text (str): The content of the reminder message (required, non-empty)
        scheduled_for (str): ISO 8601 datetime string when reminder should trigger
                           Example: "2024-01-15T10:30:00Z" or "2024-01-15T10:30:00+00:00"
        process_name (str): Name of the process scheduling the reminder (required, non-empty)
        agent_id (str, optional): ID of the agent scheduling the reminder. Defaults to "letta_agent"
        promptyoself_port (int, optional): Port where PromptyoSelf server is running. Defaults to 5000
    
    Returns:
        str: JSON string containing either success confirmation with reminder details
             or error message if the operation failed
    
    Example:
        >>> result = schedule_promptyoself_reminder(
        ...     reminder_text="Follow up on project status",
        ...     scheduled_for="2024-01-15T10:30:00Z",
        ...     process_name="project_management"
        ... )
        >>> print(result)
        {"status": "success", "message": "Reminder created successfully", "reminder_id": 123}
    """
    
    # Validate required parameters
    if not reminder_text or not reminder_text.strip():
        return json.dumps({
            "status": "error",
            "message": "reminder_text is required and must be non-empty"
        })
    
    if not scheduled_for or not scheduled_for.strip():
        return json.dumps({
            "status": "error", 
            "message": "scheduled_for is required and must be non-empty"
        })
    
    if not process_name or not process_name.strip():
        return json.dumps({
            "status": "error",
            "message": "process_name is required and must be non-empty"
        })
    
    # Validate datetime format
    try:
        # Test parsing the datetime string
        datetime.fromisoformat(scheduled_for.replace('Z', '+00:00'))
    except (ValueError, TypeError) as e:
        return json.dumps({
            "status": "error",
            "message": f"scheduled_for must be a valid ISO 8601 datetime string. Error: {str(e)}"
        })
    
    # Get API key from environment
    api_key = os.getenv("INTERNAL_AGENT_API_KEY")
    if not api_key:
        return json.dumps({
            "status": "error",
            "message": "INTERNAL_AGENT_API_KEY environment variable is not set. Please configure the API key to use this tool."
        })
    
    # Construct API endpoint URL
    api_url = f"http://localhost:{promptyoself_port}/api/internal/agents/reminders"
    
    # Prepare request headers
    headers = {
        "Content-Type": "application/json",
        "X-Agent-API-Key": api_key
    }
    
    # Prepare request payload
    payload = {
        "agent_id": agent_id,
        "reminder_text": reminder_text.strip(),
        "scheduled_for": scheduled_for.strip(),
        "process_name": process_name.strip()
    }
    
    try:
        # Make the API request
        response = requests.post(
            api_url,
            headers=headers,
            json=payload,
            timeout=10  # 10 second timeout
        )
        
        # Check if request was successful
        if response.status_code == 201:
            # Parse the successful response
            response_data = response.json()
            return json.dumps({
                "status": "success",
                "message": "Reminder created successfully",
                "reminder_id": response_data.get("reminder", {}).get("id"),
                "scheduled_for": response_data.get("reminder", {}).get("scheduled_for"),
                "process_name": response_data.get("reminder", {}).get("process_name")
            })
        else:
            # Handle API error responses
            try:
                error_data = response.json()
                error_message = error_data.get("error", f"HTTP {response.status_code}")
            except:
                error_message = f"HTTP {response.status_code}: {response.text[:200]}"
            
            return json.dumps({
                "status": "error",
                "message": f"API request failed: {error_message}",
                "http_status": response.status_code
            })
    
    except requests.exceptions.ConnectionError:
        return json.dumps({
            "status": "error",
            "message": f"Could not connect to PromptyoSelf server at localhost:{promptyoself_port}. Please ensure the server is running."
        })
    
    except requests.exceptions.Timeout:
        return json.dumps({
            "status": "error",
            "message": "Request to PromptyoSelf API timed out. The server may be overloaded."
        })
    
    except requests.exceptions.RequestException as e:
        return json.dumps({
            "status": "error",
            "message": f"Network error occurred: {str(e)}"
        })
    
    except Exception as e:
        return json.dumps({
            "status": "error",
            "message": f"Unexpected error: {str(e)}"
        })


# Letta tool metadata (for tool registration)
# This would typically be used by Letta to understand the tool's interface
TOOL_METADATA = {
    "name": "schedule_promptyoself_reminder",
    "description": "Schedule a reminder using the PromptyoSelf internal API. This tool allows the agent to create reminders that will be processed by the PromptyoSelf system at a specified time.",
    "parameters": {
        "type": "object",
        "properties": {
            "reminder_text": {
                "type": "string",
                "description": "The content of the reminder message (required, non-empty)"
            },
            "scheduled_for": {
                "type": "string", 
                "description": "ISO 8601 datetime string when reminder should trigger (e.g., '2024-01-15T10:30:00Z')"
            },
            "process_name": {
                "type": "string",
                "description": "Name of the process scheduling the reminder (required, non-empty)"
            },
            "agent_id": {
                "type": "string",
                "description": "ID of the agent scheduling the reminder (optional, defaults to 'letta_agent')",
                "default": "letta_agent"
            },
            "promptyoself_port": {
                "type": "integer",
                "description": "Port where PromptyoSelf server is running (optional, defaults to 5000)",
                "default": 5000
            }
        },
        "required": ["reminder_text", "scheduled_for", "process_name"]
    }
}


def main():
    """
    Example usage and testing function.
    This demonstrates how the tool would be used in practice.
    """
    print("PromptyoSelf Reminder Tool Example")
    print("=" * 40)
    
    # Check if API key is configured
    api_key = os.getenv("INTERNAL_AGENT_API_KEY")
    if not api_key:
        print("❌ INTERNAL_AGENT_API_KEY environment variable is not set")
        print("   Please set this variable to test the tool")
        return
    
    print("✅ API key is configured")
    
    # Example tool usage
    example_result = schedule_promptyoself_reminder(
        reminder_text="Test reminder from Letta agent",
        scheduled_for="2024-12-31T23:59:00Z",
        process_name="test_process",
        agent_id="example_agent"
    )
    
    print("\nExample tool call result:")
    print(example_result)
    
    # Parse and display the result nicely
    try:
        result_data = json.loads(example_result)
        if result_data.get("status") == "success":
            print("\n✅ Tool call succeeded!")
            print(f"   Reminder ID: {result_data.get('reminder_id')}")
        else:
            print("\n❌ Tool call failed:")
            print(f"   Error: {result_data.get('message')}")
    except json.JSONDecodeError:
        print("\n⚠️  Could not parse tool result as JSON")


if __name__ == "__main__":
    main()