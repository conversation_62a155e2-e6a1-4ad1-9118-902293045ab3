#!/usr/bin/env python3
"""
Comprehensive test script for the PromptyoSelf reminder tool.
Tests various scenarios including success, authentication failure, and validation errors.
"""

import os
import json
from promptyoself_reminder_tool import schedule_promptyoself_reminder

def test_validation_errors():
    """Test various validation error scenarios."""
    print("\n=== VALIDATION ERROR TESTS ===")
    
    # Test 1: Missing reminder_text
    print("\nTest 1: Missing reminder_text")
    result = schedule_promptyoself_reminder(
        reminder_text="",
        scheduled_for="2024-12-31T23:59:00Z",
        process_name="test_process"
    )
    result_data = json.loads(result)
    print(f"Result: {result_data['status']}")
    print(f"Message: {result_data['message']}")
    assert result_data['status'] == 'error'
    assert 'reminder_text' in result_data['message']
    print("✅ PASS: Empty reminder_text validation")
    
    # Test 2: Missing scheduled_for
    print("\nTest 2: Missing scheduled_for")
    result = schedule_promptyoself_reminder(
        reminder_text="Test reminder",
        scheduled_for="",
        process_name="test_process"
    )
    result_data = json.loads(result)
    print(f"Result: {result_data['status']}")
    print(f"Message: {result_data['message']}")
    assert result_data['status'] == 'error'
    assert 'scheduled_for' in result_data['message']
    print("✅ PASS: Empty scheduled_for validation")
    
    # Test 3: Missing process_name
    print("\nTest 3: Missing process_name")
    result = schedule_promptyoself_reminder(
        reminder_text="Test reminder",
        scheduled_for="2024-12-31T23:59:00Z",
        process_name=""
    )
    result_data = json.loads(result)
    print(f"Result: {result_data['status']}")
    print(f"Message: {result_data['message']}")
    assert result_data['status'] == 'error'
    assert 'process_name' in result_data['message']
    print("✅ PASS: Empty process_name validation")
    
    # Test 4: Invalid datetime format
    print("\nTest 4: Invalid datetime format")
    result = schedule_promptyoself_reminder(
        reminder_text="Test reminder",
        scheduled_for="invalid-datetime",
        process_name="test_process"
    )
    result_data = json.loads(result)
    print(f"Result: {result_data['status']}")
    print(f"Message: {result_data['message']}")
    assert result_data['status'] == 'error'
    assert 'ISO 8601' in result_data['message']
    print("✅ PASS: Invalid datetime validation")

def test_missing_api_key():
    """Test behavior when API key is missing."""
    print("\n=== MISSING API KEY TEST ===")
    
    # Temporarily remove API key
    original_key = os.environ.get('INTERNAL_AGENT_API_KEY')
    if 'INTERNAL_AGENT_API_KEY' in os.environ:
        del os.environ['INTERNAL_AGENT_API_KEY']
    
    result = schedule_promptyoself_reminder(
        reminder_text="Test reminder",
        scheduled_for="2024-12-31T23:59:00Z",
        process_name="test_process"
    )
    result_data = json.loads(result)
    print(f"Result: {result_data['status']}")
    print(f"Message: {result_data['message']}")
    assert result_data['status'] == 'error'
    assert 'INTERNAL_AGENT_API_KEY' in result_data['message']
    print("✅ PASS: Missing API key validation")
    
    # Restore API key
    if original_key:
        os.environ['INTERNAL_AGENT_API_KEY'] = original_key

def test_connection_error():
    """Test behavior when server is not reachable."""
    print("\n=== CONNECTION ERROR TEST ===")
    
    # Set API key
    os.environ['INTERNAL_AGENT_API_KEY'] = 'test-api-key-for-verification'
    
    # Test with non-existent port
    result = schedule_promptyoself_reminder(
        reminder_text="Test reminder",
        scheduled_for="2024-12-31T23:59:00Z",
        process_name="test_process",
        promptyoself_port=9999  # Non-existent port
    )
    result_data = json.loads(result)
    print(f"Result: {result_data['status']}")
    print(f"Message: {result_data['message']}")
    assert result_data['status'] == 'error'
    assert 'connect' in result_data['message'].lower()
    print("✅ PASS: Connection error handling")

def test_with_api_key():
    """Test with proper API key set (may fail if server issues exist)."""
    print("\n=== API KEY AUTHENTICATION TEST ===")
    
    # Set API key
    os.environ['INTERNAL_AGENT_API_KEY'] = 'test-api-key-for-verification'
    
    result = schedule_promptyoself_reminder(
        reminder_text="Test reminder from comprehensive test",
        scheduled_for="2024-12-31T23:59:00Z",
        process_name="comprehensive_test",
        agent_id="test_agent"
    )
    result_data = json.loads(result)
    print(f"Result: {result_data['status']}")
    print(f"Message: {result_data['message']}")
    
    if result_data['status'] == 'success':
        print("✅ PASS: Successful reminder creation")
        print(f"   Reminder ID: {result_data.get('reminder_id')}")
    else:
        print("❌ FAIL: Expected success but got error")
        print(f"   Error details: {result_data}")
        if 'http_status' in result_data:
            print(f"   HTTP Status: {result_data['http_status']}")

def test_invalid_api_key():
    """Test with invalid API key."""
    print("\n=== INVALID API KEY TEST ===")
    
    # Set invalid API key
    os.environ['INTERNAL_AGENT_API_KEY'] = 'invalid-api-key'
    
    result = schedule_promptyoself_reminder(
        reminder_text="Test reminder",
        scheduled_for="2024-12-31T23:59:00Z",
        process_name="test_process"
    )
    result_data = json.loads(result)
    print(f"Result: {result_data['status']}")
    print(f"Message: {result_data['message']}")
    
    if result_data['status'] == 'error':
        if 'http_status' in result_data and result_data['http_status'] == 401:
            print("✅ PASS: Invalid API key properly rejected")
        else:
            print("⚠️  Expected 401 auth error, got different error")
    else:
        print("❌ FAIL: Invalid API key was accepted")

def main():
    """Run all tests."""
    print("PromptyoSelf Reminder Tool - Comprehensive Test Suite")
    print("=" * 60)
    
    try:
        # Run validation tests (these should always pass)
        test_validation_errors()
        test_missing_api_key()
        test_connection_error()
        
        # Run API tests (these depend on server status)
        test_invalid_api_key()
        test_with_api_key()
        
        print("\n" + "=" * 60)
        print("✅ All validation tests completed successfully!")
        print("🔍 API tests completed - check individual results above")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()